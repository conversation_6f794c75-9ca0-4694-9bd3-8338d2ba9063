const express = require("express");
const {
  getEvents,
  getEventById,
  createEvent,
  updateEvent,
  deleteEvent,
  searchEvents,
  getDepartments,
  hideEvent,
} = require("../controllers/eventsController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

// 獲取所有事件
router.get("/getEvents", authMiddleware, getEvents);

// 獲取部門列表和顏色
router.get("/getDepartments", authMiddleware, getDepartments);

// 搜尋事件
router.get("/searchEvents", authMiddleware, searchEvents);

// 獲取事件詳情
router.get("/getEventById/:id", authMiddleware, getEventById);

// 新增事件
router.post("/createEvent", authMiddleware, createEvent);

// 更新事件
router.put("/updateEvent/:id", authMiddleware, updateEvent);

// 隱藏事件
router.put("/hideEvent/:id", authMiddleware, hideEvent);

// 刪除事件
router.delete("/deleteEvent/:id", authMiddleware, deleteEvent);

module.exports = router;
