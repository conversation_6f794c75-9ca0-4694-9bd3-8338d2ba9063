<template>
  <q-page class="q-pa-md flex flex-center" style="max-width: 100vw">
    <div style="width: 100%; max-width: 700px">
      <div class="text-h5 text-primary text-center q-mb-md">改版資訊</div>

      <div v-for="(update, index) in updates" :key="index" class="q-mb-md">
        <q-card flat bordered class="q-pa-sm">
          <q-card-section class="row items-center">
            <q-avatar
              :icon="update.icon"
              :color="update.color"
              text-color="white"
              size="38px"
              class="q-mr-sm"
            />
            <div class="col">
              <div class="text-h6">{{ update.version }}</div>
              <div class="text-caption text-grey">{{ update.date }}</div>
            </div>
          </q-card-section>

          <q-separator spaced />

          <q-card-section class="q-pt-none">
            <q-list dense>
              <q-item v-for="(feature, i) in update.features" :key="i">
                <q-item-section avatar>
                  <q-icon name="chevron_right" size="18px" color="secondary" />
                </q-item-section>
                <q-item-section>
                  <div class="text-body2">{{ feature }}</div>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref } from "vue";

const updates = ref([
  {
    version: "新增 行事曆 行事曆設定",
    date: "2025-08-01~2025-08-04",
    icon: "add",
    color: "red",
    features: ["行事曆:UI+API", "行事曆設定:UI+API"],
  },
  {
    version: "修改 附件上傳 新增出勤紀錄",
    date: "2025-07-31",
    icon: "warning",
    color: "warning",
    features: ["附件上傳:未正確存至子目錄", "出勤紀錄:UI出勤紀錄:UI"],
  },
  {
    version: "修改 公告列表 公告管理 新增 休假統計",
    date: "2025-07-30",
    icon: "warning",
    color: "warning",
    features: [
      "公告列表:查詢功能錯誤修正",
      "公告管理:修改權限",
      "休假統計:新增部門主管權限",
    ],
  },
  {
    version: "新增 薪資單 休假統計",
    date: "2025-07-26~2025-07-29",
    icon: "add",
    color: "red",
    features: ["薪資單:UI +API", "休假統計:UI +API"],
  },
  {
    version: "修改 Google評論",
    date: "2025-07-25",
    icon: "edit",
    color: "green",
    features: ["Google評論:UI 重新設計"],
  },
  {
    version:
      "修改 公告管理 公告列表 新增 行政公告-通知紀錄 使用者群組 部門管理 群組管理 電子公文 首頁",
    date: "2025-07-24",
    icon: "edit",
    color: "green",
    features: [
      "公告管理:重新規劃觸及人員儲存方式 新增發送公告郵件通知 修正圖片繼承其父元素的對齊方式",
      "公告列表:修正圖片繼承其父元素的對齊方式",
      "簽核通知:UI API",
      "使用者群組:使用者群組由欄位改成資料表",
      "部門管理:UI重新設計",
      "群組管理:UI重新設計 新增群組類別",
      "電子公文:更改架構 主檔存使用者id",
      "首頁:主選單權限優化 不可看且禁止外連",
    ],
  },
  {
    version: "修改 簽核表單 簽核通知 新增 電子表單-通知紀錄",
    date: "2025-07-23",
    icon: "edit",
    color: "green",
    features: [
      "通知紀錄:UI API",
      "簽核表單:LINE跟帳號登入 優化判斷方式顯示待簽核",
      "簽核通知:修改通知時機 發送 流程判斷 簽核完畢 通知介面優化",
    ],
  },
  {
    version: "修改 表單申請 使用者管理",
    date: "2025-07-22",
    icon: "edit",
    color: "green",
    features: [
      "使用者管理:UI重新設計",
      "單據查詢:修正查詢權限讀取數據",
      "紀錄:useFormFlows共用表單流程 ",
    ],
  },

  {
    version: "修改 表單查詢 門市管理 商品統計 通知中心 表單簽核",
    date: "2025-07-21",
    icon: "edit",
    color: "green",
    features: [
      "表單查詢:UI重新設計 加入可以授權查看功能",
      "門市管理:UI重新設計 預設篩選營業中",
      "商品統計:修改全畫面邏輯",
      "通知中心:新增通知中心 表單 公告 整合",
      "表單簽核:UI重新設計",
    ],
  },
  {
    version: "修改 集團分析-商品群組",
    date: "2025-07-20",
    icon: "edit",
    color: "green",
    features: ["商品群組:新增不加入合計判斷 不顯示門市合計判斷 不顯示群組判斷"],
  },
  {
    version: "新增 集團分析-商品統計 集團分析-商品群組",
    date: "2025-07-19",
    icon: "add",
    color: "red",
    features: ["商品統計:UI設計+API", "商品群組:UI設計+API"],
  },
  {
    version: "修改 行政公告 表單管理  (cursor)",
    date: "2025-07-17",
    icon: "edit",
    color: "green",
    features: [
      "行政公告:新增展開頁面",
      "表單管理:對應欄位統一管理 新增閱讀權限",
      "後端:行政公告",
    ],
  },
  {
    version: "修改 行政公告  (cursor)",
    date: "2025-07-16",
    icon: "edit",
    color: "green",
    features: ["行政公告:介面重新設計", "後端:行政公告"],
  },
  {
    version: "新增 行政公告  (cursor)",
    date: "2025-07-15",
    icon: "add",
    color: "red",
    features: ["行政公告:UI+API", "後端:行政公告"],
  },
  {
    version: "架構 行政公告  (cursor)",
    date: "2025-07-14",
    icon: "add",
    color: "red",
    features: ["行政公告:UI"],
  },
  {
    version: "新增 口味統計 編輯 菜單管理 (cursor)",
    date: "2025-07-13",
    icon: "add",
    color: "red",
    features: ["口味統計:UI+API", "菜單管理:套餐顯示", "後端:口味統計"],
  },
  {
    version: "新增 套餐管理 (cursor)",
    date: "2025-07-12",
    icon: "add",
    color: "red",
    features: [
      "套餐管理:UI+API",
      "點餐畫面:增加套餐選項",
      "後端:套餐管理 點餐畫面 列印  ",
    ],
  },
  {
    version: "新增 實際編號 票券管理(cursor)",
    date: "2025-07-11",
    icon: "add",
    color: "red",
    features: ["實際編號:UI+API", "票券管理:UI+API", "後端:實際編號 票券管理 "],
  },
  {
    version: "修改 KeepAlive 商品管理 類部口味 廚房部門 新增 其他付款(cursor)",
    date: "2025-07-10",
    icon: "warning",
    color: "warning",
    features: [
      "Bug:Line登入 KeepAlive無法正常運作 類部存檔排序異常 編輯商品管理實際編號異常 口味群組已選取需要篩選 編輯存檔按鈕置底 廚房部門 出據主機必填",
      "商品管理:規格管理",
      "其他付款:UI+API",
      "後端:其他付款",
    ],
  },
  {
    version: "修改 商品管理 頁面權限 新增 消費方式(cursor)",
    date: "2025-07-09",
    icon: "warning",
    color: "warning",
    features: [
      "Bug:商品位置數字錯誤 無法儲存 規格排序異常 刪除功能 路由錯誤",
      "消費方式:UI+API",
      "頁面權限:選擇頁面選擇方式重新設計",
      "後端:商品管理 消費方式",
    ],
  },
  {
    version: "新增 商品管理(cursor)",
    date: "2025-07-08",
    icon: "add",
    color: "red",
    features: ["商品管理:UI+API", "後端:商品管理 "],
  },
  {
    version: "修改 類部口味 出據主機",
    date: "2025-07-04",
    icon: "edit",
    color: "green",
    features: ["類部口味:API", "出據主機:設計UI API", "後端:類部口味 出據主機"],
  },
  {
    version: "修改 營業匯總 口味群組 類部口味",
    date: "2025-07-03",
    icon: "warning",
    color: "warning",
    features: [
      "Bug:營業匯總連線錯誤時loading不會消失",
      "類部口味:設計UI",
      "口味群組:功能API",
      "後端:點餐歷史 口味群組",
    ],
  },
  {
    version: "修改 開桌選單 新增 口味群組",
    date: "2025-07-02",
    icon: "warning",
    color: "warning",
    features: [
      "口味群組:UI設計 API",
      "Bug:負數金額購物車處理異常",
      "開桌狀況:開桌選單【UI重新設計】",
      "後端:載入vue-draggable-plus 口味API",
    ],
  },
  {
    version: "修改 Google評論抓取 點餐畫面",
    date: "2025-07-01",
    icon: "warning",
    color: "warning",
    features: [
      "點餐畫面:點餐【新增 口味群組】",
      "Bug:Google評論日期判斷異常",
      "後端:TasteGroup TasteItems TasteMap 明訊印表優化 新增第一輪列印消費方式",
    ],
  },
  {
    version: "新增 連線池",
    date: "2025-06-29",
    icon: "add",
    color: "red",
    features: ["後端:增加連線池"],
  },
  {
    version: "修改 點餐畫面 開桌狀況",
    date: "2025-06-28",
    icon: "edit",
    color: "green",
    features: [
      "點餐畫面:點餐【新增 數量輸入】 點餐歷史【新增API】",
      "開桌狀況:重新設計UI 根據裝置不同排列",
      "後端:點餐歷史",
    ],
  },
  {
    version: "修改 點餐畫面 開桌狀況",
    date: "2025-06-27",
    icon: "edit",
    color: "green",
    features: [
      "點餐畫面:介面UI重新設計",
      "開桌狀況:單點模式【新增 帶入預設編號】開桌選項【開關】",
      "後端:讀取菜單修改排序",
    ],
  },
  {
    version: "修改 點餐畫面",
    date: "2025-06-25",
    icon: "edit",
    color: "green",
    features: ["點餐畫面:讀取菜單【API】點餐【API】", "後端:讀取菜單"],
  },
  {
    version: "新增 點餐畫面 開桌狀況",
    date: "2025-06-23",
    icon: "add",
    color: "red",
    features: ["開桌狀況:點餐【頁面跳轉 】"],
  },
  {
    version: "修改 開桌狀況 KeepAlive",
    date: "2025-06-21",
    icon: "edit",
    color: "green",
    features: [
      "開桌狀況:服務鈴【置頂顯示 取消功能】",
      "KeepAlive:檢查token自動續1H",
      "後端:取消服務鈴",
    ],
  },
  {
    version: "紀錄 開桌狀況",
    date: "2025-06-20",
    icon: "priority_high",
    color: "info",
    features: ["紀錄:class=scroll-container 防止IOS滑動穿透(未實裝)"],
  },
  {
    version: "修改 開桌狀況 首頁",
    date: "2025-06-19",
    icon: "warning",
    color: "warning",
    features: [
      "Bug:沒有出單ip導致錯誤 修正檢查DB時無法連線崩潰",
      "開桌狀況:設定【明訊出單API 設定檔】",
      "首頁:更改登入邏輯 避免資料外洩",
      "後端:明訊出單API",
      "紀錄:更改列印邏輯 過濾有ip且prn設定列印",
    ],
  },
  {
    version: "修改 開桌狀況",
    date: "2025-06-18",
    icon: "edit",
    color: "green",
    features: [
      "開桌狀況:列印【新增輪數 版面調整】設定【桌況顏色改HEX並預覽 讀取設定 儲存設定 排序功能API】",
      "後端:輪數 列印錯誤 建立列印駐列機制 建立出單機檢查機制 列印錯誤回寫資料庫 門市建立資料表Setting 總部建立PrintError資料表 設定儲存 設定讀取 排序功能 Setting&Vclass_net檢查",
      "紀錄:出單機狀態匯入",
    ],
  },
  {
    version: "修改 開桌狀況",
    date: "2025-06-17",
    icon: "warning",
    color: "warning",
    features: [
      "Bug:桌況顏色跨日後異常",
      "開桌狀況:開桌選項送單【訂單明細主檔&明細寫入API 列印API】【優化開桌盤若無資料不顯示】【斷線時桌況隱藏】",
      "後端:訂單主檔 訂單明細 列印 門市建立資料表Vclass_net",
    ],
  },
  {
    version: "修改 開桌狀況",
    date: "2025-06-16",
    icon: "edit",
    color: "green",
    features: [
      "開桌狀況:開桌【api】開桌選項【api】",
      "後端:開桌功能確認 開桌鍋 開桌盤 ",
      "紀錄:資料庫連線數做連線池控管(代辦)",
    ],
  },
  {
    version: "修改 開桌狀況",
    date: "2025-06-15",
    icon: "edit",
    color: "green",
    features: [
      "開桌狀況:開桌【api】",
      "後端:修改TS跨日判斷邏輯 合併取號邏輯 開桌功能",
      "紀錄:跨日判斷是為了取單號",
    ],
  },
  {
    version: "修改 開桌狀況",
    date: "2025-06-14",
    icon: "edit",
    color: "green",
    features: [
      "開桌狀況:載入【消費方式 桌號頁面API】開桌頁面 【優化顯示方式】",
      "後端:消費方式 開桌頁面 TS查詢",
    ],
  },
  {
    version: "新增 線上門市-開桌狀況 系統設定-線上使用者",
    date: "2025-06-13",
    icon: "add",
    color: "red",
    features: [
      "開桌狀況:查詢【API】",
      "線上使用者:查詢【API】",
      "改版資訊:版面變更",
      "後端:開桌狀況 線上使用者",
    ],
  },
  {
    version: "修改 門市相關-報表分析-時段統計",
    date: "2025-06-12",
    icon: "edit",
    color: "green",
    features: ["類部統計:查詢【API】【圖表列表切換】", "後端:時段統計"],
  },
  {
    version: "新增 門市相關-報表分析-時段統計",
    date: "2025-06-11",
    icon: "edit",
    color: "green",
    features: ["類部統計:查詢【UI】"],
  },
  {
    version: "新增 門市相關-報表分析-類部統計",
    date: "2025-06-10",
    icon: "add",
    color: "red",
    features: ["類部統計:查詢【API】", "後端:類部統計"],
  },
  {
    version: "修改 彙總報表 各日曆查詢 門市管理",
    date: "2025-06-09",
    icon: "warning",
    color: "warning",
    features: [
      "Bug:日曆不能切換月份拿掉關閉按鈕 修改密碼欄位格式錯誤",
      "使用者管理:恢復密碼【API】",
      "彙總報表:銷售禮券 未開【API】作廢發票金額【API 新增顯示明細】明細展開【邏輯更改】",
      "門市管理:差勤設備IP【新增】 Google評論【新增】",
      "後端:彙總表查詢 修正 門市管理API 使用者管理API",
    ],
  },
  {
    version: "新增 門市相關-報表分析-彙總報表",
    date: "2025-06-08",
    icon: "add",
    color: "red",
    features: ["彙總報表:查詢【API】", "後端:彙總表查詢"],
  },
  {
    version: "修改 首頁 使用者管理",
    date: "2025-06-07",
    icon: "edit",
    color: "green",
    features: [
      "首頁:門市選擇【新增授權門市API僅能查詢授權門市】使用者管理:授權門市【新增 API】",
      "後端:授權門市 使用者門市授權",
      "優化:單據查詢開Loading優化",
      "紀錄:Admin不受權限影響",
    ],
  },
  {
    version: "修改 門市管理-新增Google評論",
    date: "2025-06-06",
    icon: "warning",
    color: "warning",
    features: [
      "Bug:門市資料 資料類別【Disable條件】",
      "門市相關:Google評論【新增分頁 API】",
      "後端:Google評論、批次抓取Google評論紀錄每天晚上11:00",
    ],
  },
  {
    version: "新增差勤管理",
    date: "2025-06-05",
    icon: "edit",
    color: "green",
    features: [
      "差勤管理:刪除設備紀錄【API】、FTP設定頁【新增】、紀錄【狀態中文名稱】、匯出資料【下載匯出沒日期防呆】",
      "後端:刪除設備紀錄、Token改1D過期",
    ],
  },
  {
    version: "新增差勤管理",
    date: "2025-06-04",
    icon: "edit",
    color: "green",
    features: [
      "差勤管理:debug門市設備無紀錄下載紀錄導致崩潰、匯出資料-FTP【API】、FTP設定頁【新增】、紀錄【狀態中文名稱】、匯出資料【下載匯出沒日期防呆】",
      "後端:先查詢設備紀錄數若是0則不下載、匯出FTP",
    ],
  },
  {
    version: "新增差勤管理",
    date: "2025-06-03",
    icon: "add",
    color: "red",
    features: [
      "差勤管理:門市選單【API】、下載紀錄【API】、同步時間【API】、輸出資料-下載【API】、批次執行【API】、刪除歷史紀錄【API】",
      "後端:門市選單、下載紀錄、同步時間、輸出資料-下載、批次執行、刪除歷史紀錄",
      "紀錄:需判斷操作裝置UTC避免時間出錯 資料庫一律儲存UTC",
    ],
  },
  {
    version: "修改門市 新增人資相關",
    date: "2025-06-02",
    icon: "edit",
    color: "green",
    features: [
      "門市管理:資料類別【未連線無法選擇】 人資相關:差勤管理【頁面新增】",
      "後端:門市資料新增差勤設備IP、差勤回收時間",
    ],
  },
  {
    version: "修改首頁",
    date: "2025-05-28",
    icon: "edit",
    color: "green",
    features: [
      "首頁:本月業績【更名本月營收】、本日營收【新增切換】【開啟swipeable可左右滑動】【新增最後更新時間】",
      "後端:查詢業績",
    ],
  },
  {
    version: "新增修改首頁",
    date: "2025-05-27",
    icon: "add",
    color: "red",
    features: [
      "首頁:本月業績【門市查詢】【月份查詢】、新增ECharts元件【折線圖】、改版資訊【移動位置】、首頁【新增按鈕】、主選單【移除首頁文字】",
      "後端:查詢業績",
    ],
  },
  {
    version: "新增發票管理",
    date: "2025-05-26",
    icon: "add",
    color: "red",
    features: [
      "發票管理:【日期查詢】【進階查詢】【作廢還原】",
      "後端:查詢發票、更改發票狀態",
    ],
  },
  {
    version: "修改消費結帳",
    date: "2025-05-22",
    icon: "edit",
    color: "green",
    features: [
      "消費結帳:訂單明細【招待顯示】、【補差額金額顯示】【招待金額顯示】 ",
      "後端:明細招待欄位R1狀態G、主檔補差額欄位Tamt、招待欄位Ramt",
    ],
  },
  {
    version: "修改消費結帳",
    date: "2025-05-21",
    icon: "edit",
    color: "green",
    features: [
      "消費結帳:訂單明細【顯示】【篩選0元】作廢還原訂單【功能寫入】 其他付款明細【顯示】 ",
      "後端:訂單作廢還原連動發票、其他付款資料、訂單明細資料、作廢還原限制Admin權限",
    ],
  },
  {
    version: "修改消費結帳",
    date: "2025-05-20",
    icon: "edit",
    color: "green",
    features: [
      "消費結帳:【基本資料顯示】新增交易日期【篩選】 新增進階篩選【交易日期、交易單號、發票號碼、統一編號】 ",
      "後端:加入抓取訂單資料",
    ],
  },
  {
    version: "架構修改",
    date: "2025-05-15",
    icon: "edit",
    color: "green",
    features: [
      "首頁:主選單【第三層Icon改由後端提供】【第一層group由Slevel控制】",
      "後端:連線狀態改由SQL連線測試",
    ],
  },
  {
    version: "新增門市相關",
    date: "2025-05-14",
    icon: "add",
    color: "red",
    features: ["門市相關:營業資料", "營業資料:消費結帳"],
  },
  {
    version: "修改門市管理",
    date: "2025-05-08",
    icon: "edit",
    color: "green",
    features: [
      "門市管理:選擇門市【改顯示門市名稱及門市代號】新增【功能寫入】、回收【功能寫入】、POS資料回收時間【修改格式】、儲存【功能寫入】",
      "後端:回收功能寫入、儲存功能寫入包含狀態判斷修改Restq新增或刪除門市",
    ],
  },
  {
    version: "修改門市管理",
    date: "2025-05-07",
    icon: "edit",
    color: "green",
    features: [
      "門市管理:資料類別【新增回收選項】、所屬部門【讀取部門代號】",
      "後端:連線狀態改由SQL連線測試",
    ],
  },
  {
    version: "修改門市管理",
    date: "2025-04-23",
    icon: "edit",
    color: "green",
    features: ["門市管理:選擇門市【篩選功能】", "後端:連線狀態改由SQL連線測試"],
  },
  {
    version: "新增門市管理",
    date: "2025-03-21",
    icon: "add",
    color: "red",
    features: ["門市管理:基本資料", "後端:新增Ping 測試門市連線狀況"],
  },
  {
    version: "架構變更",
    date: "2025-03-14",
    icon: "swap_vert",
    color: "info",
    features: [
      "首頁:動態產生【主選單】",
      "權限管理:更名【頁面權限】",
      "頁面權限:資料欄位新增:Color Badge Parent_id Slevel Icon 讀取寫入",
    ],
  },
  {
    version: "基礎建構",
    date: "2025-03-13",
    icon: "cloud",
    color: "primary",
    features: [
      "首頁:新增【改版資訊】",
      "表單申請:差假申請、加班申請",
      "簽核",
      "查詢",
      "流程:新增【部門主管(階層)】，往上一層部門主管簽核",
      "流程:新增【通知】開關，若使用者有Email會發送通知",
      "使用者:新增【E-Mail】",
      "部門管理:新增【上層部門】",
      "群組管理",
      "權限管理",
    ],
  },
]);
</script>
<style>
.feature-list {
  margin: 0;
  padding-left: 1.2rem;
  font-size: 14px;
  line-height: 1.6;
}
.feature-list li {
  margin-bottom: 6px;
  list-style-type: disc;
  color: #333;
}
</style>
