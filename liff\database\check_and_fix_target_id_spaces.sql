-- 檢查和修正 target_id 中的空格問題

-- 1. 檢查 Events_targets 表中的 target_id 空格問題
SELECT 
    'Events_targets' as table_name,
    target_type,
    target_id,
    LEN(target_id) as target_id_length,
    ASCII(LEFT(target_id, 1)) as first_char_ascii,
    ASCII(RIGHT(target_id, 1)) as last_char_ascii,
    CASE 
        WHEN target_id = 'ALL' THEN 'EXACT_MATCH'
        WHEN LTRIM(RTRIM(target_id)) = 'ALL' THEN 'TRIMMED_MATCH'
        ELSE 'NO_MATCH'
    END as all_match_status,
    CASE 
        WHEN target_id = 'OWN' THEN 'EXACT_MATCH'
        WHEN LTRIM(RTRIM(target_id)) = 'OWN' THEN 'TRIMMED_MATCH'
        ELSE 'NO_MATCH'
    END as own_match_status
FROM Events_targets 
WHERE target_id LIKE '%ALL%' OR target_id LIKE '%OWN%'

UNION ALL

-- 2. 檢查 Events_access 表中的 target_id 空格問題
SELECT 
    'Events_access' as table_name,
    target_type,
    target_id,
    LEN(target_id) as target_id_length,
    ASCII(LEFT(target_id, 1)) as first_char_ascii,
    ASCII(RIGHT(target_id, 1)) as last_char_ascii,
    CASE 
        WHEN target_id = 'ALL' THEN 'EXACT_MATCH'
        WHEN LTRIM(RTRIM(target_id)) = 'ALL' THEN 'TRIMMED_MATCH'
        ELSE 'NO_MATCH'
    END as all_match_status,
    CASE 
        WHEN target_id = 'OWN' THEN 'EXACT_MATCH'
        WHEN LTRIM(RTRIM(target_id)) = 'OWN' THEN 'TRIMMED_MATCH'
        ELSE 'NO_MATCH'
    END as own_match_status
FROM Events_access 
WHERE target_id LIKE '%ALL%' OR target_id LIKE '%OWN%'
ORDER BY table_name, target_type, target_id;

-- 3. 修正 Events_targets 表中的空格問題
UPDATE Events_targets 
SET target_id = LTRIM(RTRIM(target_id))
WHERE target_id != LTRIM(RTRIM(target_id));

-- 4. 修正 Events_access 表中的空格問題
UPDATE Events_access 
SET target_id = LTRIM(RTRIM(target_id))
WHERE target_id != LTRIM(RTRIM(target_id));

-- 5. 檢查修正後的結果
SELECT 
    'AFTER_FIX_Events_targets' as table_name,
    COUNT(*) as total_count,
    SUM(CASE WHEN target_id = 'ALL' THEN 1 ELSE 0 END) as all_count,
    SUM(CASE WHEN target_id = 'OWN' THEN 1 ELSE 0 END) as own_count
FROM Events_targets

UNION ALL

SELECT 
    'AFTER_FIX_Events_access' as table_name,
    COUNT(*) as total_count,
    SUM(CASE WHEN target_id = 'ALL' THEN 1 ELSE 0 END) as all_count,
    SUM(CASE WHEN target_id = 'OWN' THEN 1 ELSE 0 END) as own_count
FROM Events_access;
