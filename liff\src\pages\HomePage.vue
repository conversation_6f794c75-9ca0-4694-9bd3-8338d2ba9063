<template>
  <q-layout view="hHh Lpr lFf">
    <!-- 左側導覽列 -->
    <q-drawer
      v-model="drawer"
      show-if-above
      bordered
      class="column no-wrap full-height"
    >
      <div class="menu-header">
        <q-banner inline-actions class="text-white bg-brown-5 text-center">
          主選單
        </q-banner>
        <q-card
          v-if="branchOptions.length > 0"
          class="q-ma-none q-pa-sm text-dark"
          square
          flat
        >
          <q-card-section class="q-pt-xs rounded-borders">
            <q-select
              dense
              square
              v-model="selectedBranch"
              :options="branchOptions"
              emit-value
              map-options
              label="選擇門市"
              color="primary"
              popup-content-class="bg-grey-1"
              @update:model-value="updateDatabase"
            >
              <template v-slot:prepend>
                <building :stroke-width="1" :size="22" />
              </template>
            </q-select>
          </q-card-section>
        </q-card>
      </div>
      <q-list class="menu-list-scroll">
        <template v-for="menu in firstLevelMenus" :key="menu.Page_id">
          <q-expansion-item
            v-if="hasPermission(menu.Page_id)"
            :label="menu.Name"
            :header-class="menu.Color"
            :group="`${menu.Slevel}`"
          >
            <!-- 第一層 header slot -->
            <template v-slot:header>
              <q-item-section avatar>
                <component :is="menu.Icon" :stroke-width="1" />
              </q-item-section>
              <q-item-section>{{ menu.Name }}</q-item-section>
            </template>

            <!-- 第二層 -->
            <template
              v-for="child in getSecondLevelMenus(menu.Page_id)"
              :key="child.Page_id"
            >
              <q-expansion-item
                v-if="
                  hasPermission(child.Page_id) && hasSubItems(child.Page_id)
                "
                :label="child.Name"
                :header-class="child.Color"
                :header-inset-level="1"
                expand-icon-class="hidden"
              >
                <template v-slot:header>
                  <q-item-section avatar>
                    <component :is="child.Icon" :stroke-width="1" />
                  </q-item-section>
                  <q-item-section>{{ child.Name }}</q-item-section>
                </template>

                <!-- 第三層 -->
                <q-expansion-item
                  v-for="subchild in getFilteredThirdLevelMenus(child.Page_id)"
                  :key="subchild.Page_id"
                  :label="subchild.Name"
                  :header-class="subchild.Color"
                  :header-inset-level="2"
                  expand-icon-class="hidden"
                  clickable
                  v-ripple
                  @click="updateContent(subchild.Page_id)"
                >
                  <template v-slot:header>
                    <q-item-section avatar>
                      <component :is="subchild.Icon" :stroke-width="1" />
                    </q-item-section>
                    <q-item-section>{{ subchild.Name }}</q-item-section>
                  </template>
                </q-expansion-item>
              </q-expansion-item>

              <!-- 沒有第三層 -->
              <q-expansion-item
                v-else-if="hasPermission(child.Page_id)"
                :label="child.Name"
                :header-class="child.Color"
                :header-inset-level="1"
                expand-icon-class="hidden"
                clickable
                v-ripple
                @click="updateContent(child.Page_id)"
              >
                <template v-slot:header>
                  <q-item-section avatar>
                    <component :is="child.Icon" :stroke-width="1" />
                  </q-item-section>
                  <q-item-section>{{ child.Name }}</q-item-section>
                </template>
              </q-expansion-item>
            </template>
          </q-expansion-item>
        </template>
      </q-list>
    </q-drawer>

    <!-- 上方橫幅 -->
    <q-header elevated>
      <q-toolbar>
        <q-btn flat dense round icon="menu" @click="drawer = !drawer" />
        <q-toolbar-title></q-toolbar-title>
        <!-- 改版資訊按鈕 -->
        <q-btn flat round dense @click="activeContent = ``" class="q-mr-sm">
          <house :size="24" />
          <q-tooltip>首頁</q-tooltip>
        </q-btn>

        <!-- 公告通知組件 -->
        <NotificationCenter
          v-if="userId"
          :userId="userId"
          :userDep="Did"
          :userPermissions="userGroups"
          :userBranches="UserBranch"
          @open-bulletins="handleOpenBulletins"
        />

        <!-- 右側使用者資訊 -->
        <q-btn-dropdown flat>
          <template v-slot:label>
            <div class="row items-center no-wrap">
              <q-avatar size="40px">
                <img v-if="user.pictureUrl" :src="user.pictureUrl" alt="頭像" />
                <q-icon
                  v-if="!user.pictureUrl"
                  name="account_circle"
                  size="40px"
                  color="grey"
                />
              </q-avatar>
              <div class="q-ml-sm">{{ user.displayName || name }}</div>
            </div>
          </template>
          <q-list>
            <q-item
              clickable
              v-close-popup
              @click="openChangePasswordDialog"
              class="text-primary"
            >
              <q-item-section>
                <div>
                  <lock-keyhole
                    :stroke-width="1"
                    :size="16"
                    class="q-mr-sm icon-center"
                  />

                  <span>修改密碼</span>
                </div>
              </q-item-section>
            </q-item>

            <q-item
              clickable
              v-close-popup
              @click="updateContent('info')"
              class="text-secondary"
            >
              <q-item-section>
                <div>
                  <book-check
                    :stroke-width="1"
                    :size="16"
                    class="q-mr-sm icon-center"
                  />
                  <span>改版資訊</span>
                </div>
              </q-item-section>
            </q-item>

            <q-separator />

            <q-item
              clickable
              v-close-popup
              @click="logoutLiff"
              class="text-negative"
            >
              <q-item-section>
                <div>
                  <log-out
                    :stroke-width="1"
                    :size="16"
                    class="q-mr-sm icon-center"
                  />
                  <span>登出</span>
                </div>
              </q-item-section>
            </q-item>
          </q-list>
        </q-btn-dropdown>
      </q-toolbar>
    </q-header>

    <!-- 內容區 -->
    <q-page-container>
      <q-page class="flex flex-center">
        <sales-line-chart v-if="activeContent === ''" />
        <EPM_01
          v-if="activeContent === 'epm_01' && hasPermission('epm_01')"
          :Dname="dname"
          :Userid="userId"
        />
        <EPM_02
          v-if="activeContent === 'epm_02' && hasPermission('epm_02')"
          :Dname="dname"
          :Userid="userId"
        />
        <EPM_03
          v-if="activeContent === 'epm_03' && hasPermission('epm_03')"
          :Dname="dname"
          :Userid="userId"
        />
        <Formsearch
          v-if="activeContent === 'formsearch' && hasPermission('formsearch')"
          :Userid="userId"
        />
        <Formsign
          v-if="activeContent === 'formsign' && hasPermission('formsign')"
          :Userid="userId"
        />
        <Users v-if="activeContent === 'users' && hasPermission('users')" />
        <Access v-if="activeContent === 'access' && hasPermission('access')" />
        <Group v-if="activeContent === 'group' && hasPermission('group')" />
        <Process
          v-if="activeContent === 'process' && hasPermission('process')"
        />
        <Dep v-if="activeContent === 'dep' && hasPermission('dep')" />
        <Info v-if="activeContent === 'info'" />
        <Branch v-if="activeContent === 'branch' && hasPermission('branch')" />
        <Order
          v-if="activeContent === 'order' && hasPermission('order')"
          :Branch="selectedBranch"
          :Permissions="userGroups"
        />
        <Minvo
          v-if="activeContent === 'minvo' && hasPermission('minvo')"
          :Branch="selectedBranch"
          :Permissions="userGroups"
        />
        <Attendance
          v-if="activeContent === 'attendance' && hasPermission('attendance')"
          :Branch="selectedBranch"
          :Permissions="userGroups"
        />
        <Comments
          v-if="activeContent === 'comments' && hasPermission('comments')"
          :Branch="selectedBranch"
        />
        <Bsummary
          v-if="activeContent === 'bsummary' && hasPermission('bsummary')"
          :Branch="selectedBranch"
        />
        <Bclassstat
          v-if="activeContent === 'bclassstat' && hasPermission('bclassstat')"
          :Branch="selectedBranch"
        />
        <Btimestat
          v-if="activeContent === 'btimestat' && hasPermission('btimestat')"
          :Branch="selectedBranch"
        />
        <Btastestat
          v-if="activeContent === 'btastestat' && hasPermission('btastestat')"
          :Branch="selectedBranch"
        />
        <Tablestatus
          v-if="activeContent === 'tablestatus' && hasPermission('tablestatus')"
          :Branch="selectedBranch"
          :IP="selectedIP"
          @openOrderPage="handleOpenOrderPage"
        />
        <Onlineusers
          v-if="activeContent === 'onlineusers' && hasPermission('onlineusers')"
        />
        <Orderinternal
          v-if="
            activeContent === 'orderinternal' && hasPermission('orderinternal')
          "
          :OrderInfo="orderInfo"
          @goBack="activeContent = 'tablestatus'"
        />
        <Otastes
          v-if="activeContent === 'otastes' && hasPermission('otastes')"
          :Branch="selectedBranch"
        />
        <Occlass
          v-if="activeContent === 'occlass' && hasPermission('occlass')"
          :Branch="selectedBranch"
        />
        <Ovclass
          v-if="activeContent === 'ovclass' && hasPermission('ovclass')"
          :Branch="selectedBranch"
        />
        <Okclass
          v-if="activeContent === 'okclass' && hasPermission('okclass')"
          :Branch="selectedBranch"
        />
        <Omenum
          v-if="activeContent === 'omenum' && hasPermission('omenum')"
          :Branch="selectedBranch"
        />
        <Omode
          v-if="activeContent === 'omode' && hasPermission('omode')"
          :Branch="selectedBranch"
        />
        <Otherpay
          v-if="activeContent === 'otherpay' && hasPermission('otherpay')"
          :Branch="selectedBranch"
        />
        <Oticket
          v-if="activeContent === 'oticket' && hasPermission('oticket')"
          :Branch="selectedBranch"
        />
        <Ormname
          v-if="activeContent === 'ormname' && hasPermission('ormname')"
          :Branch="selectedBranch"
        />
        <Osets
          v-if="activeContent === 'osets' && hasPermission('osets')"
          :Branch="selectedBranch"
        />
        <Btinmanage
          v-if="activeContent === 'btinmanage' && hasPermission('btinmanage')"
          :Userid="userId"
          :Permissions="userGroups"
          :Dname="dname"
          :Did="Did"
        />
        <Btinlist
          v-if="activeContent === 'btinlist' && hasPermission('btinlist')"
          :Userid="userId"
          :Permissions="userGroups"
          :Dname="dname"
          :Did="Did"
          :UserBranch="UserBranch"
        />
        <Gcountgroup
          v-if="activeContent === 'gcountgroup' && hasPermission('gcountgroup')"
          :Branch="selectedBranch"
        />
        <Gcountstat
          v-if="activeContent === 'gcountstat' && hasPermission('gcountstat')"
          :Branch="selectedBranch"
        />
        <Formnotify
          v-if="activeContent === 'formnotify' && hasPermission('formnotify')"
          :Branch="selectedBranch"
          :Permissions="userGroups"
        />
        <Btinnotify
          v-if="activeContent === 'btinnotify' && hasPermission('btinnotify')"
        />
        <UpayslipPage
          v-if="activeContent === 'upayslip' && hasPermission('upayslip')"
          :Userid="userId"
        />
        <Uleave
          v-if="activeContent === 'uleave' && hasPermission('uleave')"
          :Userid="userId"
        />
        <Attrecords
          v-if="activeContent === 'attrecords' && hasPermission('attrecords')"
          :Userid="userId"
        />
        <Calendar
          v-if="activeContent === 'calendar' && hasPermission('calendar')"
          :userId="userId"
          :userDep="Did"
          :userBranch="selectedBranch"
          :userPermissions="userGroups"
          :BranchOpts="branchOptions"
          :Dname="dname"
        />
        <Calendarset
          v-if="activeContent === 'calendarset' && hasPermission('calendarset')"
        />
      </q-page>
    </q-page-container>
  </q-layout>
  <!-- 修改密碼 Dialog -->
  <q-dialog v-model="changePasswordDialog">
    <q-card style="min-width: 300px; max-width: 400px">
      <q-card-section class="text-center q-pt-md q-pb-sm">
        <lock-keyhole
          :stroke-width="1"
          :size="36"
          class="q-mb-xs text-primary"
        />
        <div class="text-h6 text-weight-medium">修改密碼</div>
      </q-card-section>

      <q-card-section class="q-gutter-y-sm">
        <q-input
          v-model="currentPassword"
          label="當前密碼"
          type="password"
          outlined
          dense
          clearable
        >
          <template v-slot:prepend>
            <key-round :stroke-width="1" :size="24" class="q-mb-xs" />
          </template>
        </q-input>
        <q-input
          v-model="newPassword"
          label="新密碼"
          type="password"
          outlined
          dense
          clearable
        >
          <template v-slot:prepend>
            <lock-keyhole :stroke-width="1" :size="24" class="q-mb-xs" />
          </template>
        </q-input>
        <q-input
          v-model="confirmPassword"
          label="確認新密碼"
          type="password"
          outlined
          dense
          clearable
          :error="
            newPassword !== '' &&
            confirmPassword !== '' &&
            confirmPassword !== newPassword
          "
          error-message="密碼不匹配，請再次確認"
        >
          <template v-slot:prepend>
            <lock-keyhole :stroke-width="1" :size="24" class="q-mb-xs" />
          </template>
        </q-input>
      </q-card-section>

      <q-separator />

      <q-card-actions class="q-px-md q-pb-md justify-end">
        <q-btn
          label="取消"
          text-color="grey-8"
          flat
          @click="changePasswordDialog = false"
        />
        <q-btn
          label="確認修改"
          color="primary"
          unelevated
          :disable="newPassword === '' || confirmPassword !== newPassword"
          @click="changePassword"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import apiClient from "../api";
import { ref, onMounted, computed, onBeforeUnmount, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useQuasar } from "quasar";
import axios from "axios";
import liff from "@line/liff";
import EPM_01 from "./EPM_01.vue";
import EPM_02 from "./EPM_02.vue";
import EPM_03 from "./EPM_03.vue";
import Formsearch from "./FormsearchPage.vue";
import Formsign from "./FormsignPage.vue";
import Users from "./UsersPage.vue";
import Access from "./AccessPage.vue";
import Group from "./GroupPage.vue";
import Process from "./ProcessPage.vue";
import Dep from "./DepPage.vue";
import Info from "./InfoPage.vue";
import Branch from "./BranchPage.vue";
import Order from "./OrderPage.vue";
import Minvo from "./MinvoPage.vue";
import Attendance from "./AttendancePage.vue";
import Comments from "./CommentsPage.vue";
import Bsummary from "./BsummaryPage.vue";
import Bclassstat from "./BclassstatPage.vue";
import Btimestat from "./BtimestatPage.vue";
import Btastestat from "./BtastestatPage.vue";
import Tablestatus from "./TablestatusPage.vue";
import Onlineusers from "./OnlineusersPage.vue";
import Orderinternal from "./OrderinternalPage.vue";
import Otastes from "./OtastesPage.vue";
import Occlass from "./OcclassPage.vue";
import Ovclass from "./OvclassPage.vue";
import Okclass from "./OkclassPage.vue";
import Omenum from "./OmenumPage.vue";
import Omode from "./OmodePage.vue";
import Otherpay from "./OtherpayPage.vue";
import Oticket from "./OticketPage.vue";
import Ormname from "./OrmnamePage.vue";
import Osets from "./OsetsPage.vue";
import Btinmanage from "./BtinmanagePage.vue";
import Btinlist from "./BtinlistPage.vue";
import Gcountgroup from "./GcountgroupPage.vue";
import Gcountstat from "./GcountstatPage.vue";
import { isTokenExpired } from "../utils/auth";
import { usePendingStore } from "../stores/pendingStore"; //✅ 引入 Pinia Store
import SalesLineChart from "components/SalesLineChart.vue";
import NotificationCenter from "../components/NotificationCenter.vue";
import Formnotify from "./FormnotifyPage.vue";
import Btinnotify from "./BtinnotifyPage.vue"; // 添加引入
import UpayslipPage from "./UpayslipPage.vue";
import Uleave from "./UleavePage.vue";
import Attrecords from "./AttrecordsPage.vue";
import Calendar from "./CalendarPage.vue";
import Calendarset from "./CalendarsetPage.vue";

const orderInfo = ref(null);
const handleOpenOrderPage = (payload) => {
  orderInfo.value = { ...payload };
  activeContent.value = "orderinternal";
};

const updateDatabase = (newBranch) => {
  const found = branchOptions.value.find((b) => b.value === newBranch);
  if (found) {
    selectedIP.value = found.ip;
  } else {
    selectedIP.value = "";
  }

  activeContent.value = "";
};

const hasSubItems = (pageId) => {
  return allpageOptions.value.some(
    (m) => m.Slevel === 3 && m.Parent_id?.trim() === pageId.trim()
  );
};
const $q = useQuasar();
const changePasswordDialog = ref(false);
const currentPassword = ref("");
const newPassword = ref("");
const confirmPassword = ref("");

const openChangePasswordDialog = () => {
  changePasswordDialog.value = true;
};
const username = ref("");
const pendingStore = usePendingStore(); // ✅ 取得 Store
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const userId = ref(null); // `users.ID` 員工編號
const router = useRouter();
const drawer = ref(false);
const activeContent = ref("");
const uid = ref("");
const name = ref("");
const dname = ref("");
const userGroups = ref([]); // 存儲用戶的所有權限群組
const Did = ref("");
const user = ref({
  isLoggedIn: false,
  userId: "",
  displayName: "",
  pictureUrl: "",
});
const selectedBranch = ref("");
const selectedIP = ref("");
const branchOptions = ref([]);
const loginMethod = ref(""); // "line" 或 "account"

const fetchBranches = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/branch/get_branch`);
    const allBranches = response.data;

    // 判斷是否有 Admin 權限
    const hasAdminPermission = userGroups.value.some(
      (group) => group.toLowerCase() === "admin"
    );

    // 過濾門市資料
    const filteredBranches = hasAdminPermission
      ? allBranches.filter((branch) => branch.Sts >= "1")
      : allBranches.filter(
          (branch) =>
            branch.Sts >= "1" &&
            UserBranch.value.includes(branch.Cod_cust.trim())
        );

    // 只有在有授權門市時才顯示選項
    if (filteredBranches.length > 0) {
      branchOptions.value = filteredBranches.map((branch) => ({
        value: branch.Cod_cust.trim(),
        label: branch.Cod_name.trim(),
        ip: branch.Ip,
      }));

      // 預設選第一個門市
      if (branchOptions.value.length > 0) {
        selectedBranch.value = branchOptions.value[0].value;
        selectedIP.value = branchOptions.value[0].ip;
        updateDatabase(selectedBranch.value);
      }
    } else {
      // 如果沒有授權門市，清空選項並顯示通知
      branchOptions.value = [];
    }
  } catch (error) {
    console.error("獲取門市資料失敗:", error);
    $q.notify({
      type: "negative",
      message: "獲取門市資料失敗，請重新登入或聯絡管理員",
      position: "top",
      timeout: 3000,
    });
    branchOptions.value = [];
  }
};

const allpageOptions = ref([]);

const fetchFormsPage = async () => {
  try {
    const response = await apiClient.get(`/page/get_FormsPage`);
    if (response.data) {
      allpageOptions.value = response.data.map((page) => ({
        ...page,
        Page_id: page.Page_id.trim(),
        Name: page.Name.trim(),
        Parent_id: page.Parent_id ? page.Parent_id.trim() : "",
        Badge: page.Badge ? page.Badge.trim() : "",
        Color: "text-" + page.Color.trim(),
        Icon: page.Icon.trim(),
        Slevel: page.Slevel,
      }));
    }
  } catch (error) {
    console.error("❌ 無法載入表單頁面:", error);
  }
};
const hasPermission = (pageId) => {
  const allowedGroups = (pagePermissions.value[pageId] || []).map((g) =>
    g.trim().toLowerCase()
  );

  // 檢查是否為 Admin
  if (userGroups.value.some((group) => group.toLowerCase() === "admin")) {
    return true;
  }

  // 檢查用戶的任何一個群組是否在允許的群組列表中
  return userGroups.value.some((group) =>
    allowedGroups.includes(group.toLowerCase())
  );
};

const pagePermissions = ref({});

const loadPagePermissions = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/page/get_FormsPage`);

    if (response.data) {
      // 轉換資料，確保 Page_id 去除空格
      pagePermissions.value = response.data.reduce((acc, page) => {
        const pageKey = page.Page_id.trim().toLowerCase(); // ✅ 去除空格
        acc[pageKey] = Array.isArray(page.Allow)
          ? page.Allow.map((group) => group.trim())
          : []; // ✅ 直接使用陣列，或在沒有權限時使用空陣列
        return acc;
      }, {});
    }
  } catch (error) {
    console.error("讀取頁面權限失敗:", error);
  }
};

const changePassword = async () => {
  try {
    if (!userId.value) {
      $q.notify({ type: "negative", message: "❌ 無法驗證用戶，請重新登入" });
      return;
    }

    const response = await axios.post(`${apiBaseUrl}/auth/change-password`, {
      userId: userId.value,
      currentPassword: currentPassword.value,
      newPassword: newPassword.value,
    });

    if (response.data.success) {
      $q.notify({ type: "positive", message: "密碼修改成功，請重新登入" });
      changePasswordDialog.value = false;
      router.push("/");
    } else {
      $q.notify({ type: "negative", message: response.data.message });
    }
  } catch (error) {
    console.error("❌ 修改密碼錯誤:", error);
    $q.notify({ type: "negative", message: "❌ 伺服器錯誤，請稍後再試" });
  }
};

// 切換內容
const updateContent = (contentName) => {
  if (contentName === "info") {
    activeContent.value = contentName;
    drawer.value = false;
    return;
  }

  // 薪資單頁面只允許 LINE 登入用戶訪問
  // if (contentName === "payslip" && loginMethod.value !== "line") {
  //   $q.notify({
  //     type: "negative",
  //     message: "薪資單功能僅限 LINE 登入用戶使用",
  //     position: "top",
  //   });
  //   return;
  // }

  // 檢查是否為 Admin
  if (userGroups.value.some((group) => group.toLowerCase() === "admin")) {
    // Admin 可以瀏覽所有頁面
    activeContent.value = contentName;
    drawer.value = false;
    return;
  }

  // 取得該頁面的權限組，確保是小寫
  const allowedGroups = (pagePermissions.value[contentName] || []).map((g) =>
    g.trim().toLowerCase()
  );

  // 檢查用戶的任何一個群組是否在允許的群組列表中
  const hasAccess = userGroups.value.some((group) =>
    allowedGroups.includes(group.toLowerCase())
  );

  if (!hasAccess) {
    $q.notify({
      type: "negative",
      message: "未授權操作，請聯繫管理員",
      position: "top",
    });
    return;
  }

  activeContent.value = contentName;
  drawer.value = false;
};

// 1️⃣ **查詢 `users` 表，透過 `UID` 找到 `ID`**
const fetchUserId = async () => {
  try {
    // 🔹 讀取 JWT Token
    const token = localStorage.getItem("jwt_token");

    // Token 驗證代碼保持不變
    // ✅ 如果 JWT 不存在，直接跳回 `/`
    if (!token) {
      console.error("❌ JWT Token 不存在，跳回登入頁");
      router.push({ path: "/", query: { expired: true } });
      return;
    }

    // ✅ 解析 JWT，檢查是否過期
    if (isTokenExpired(token)) {
      console.error("❌ JWT 已過期，清除 Token 並跳回登入頁");
      localStorage.removeItem("jwt_token");
      router.push({ path: "/", query: { expired: true } });
      return;
    }

    const verifyResponse = await axios.post(`${apiBaseUrl}/auth/verify-token`, {
      token,
    });

    if (!verifyResponse.data.success) {
      console.error("❌ JWT 無效，清除 Token 並跳回登入頁");
      localStorage.removeItem("jwt_token");
      router.push({ path: "/", query: { expired: true } });
      return;
    }
    const response = await apiClient.post(`${apiBaseUrl}/users/users`, {
      uid: uid.value,
    });

    if (response.data && response.data.length > 0) {
      userId.value = response.data[0].ID; // ✅ 取得對應的員工編號
      name.value = response.data[0].Name;
      dname.value = response.data[0].Dname;
      Did.value = response.data[0].Did;

      // 僅獲取用戶所有權限群組，不再依賴 Usergroup 欄位
      await fetchUserGroups(userId.value);

      await pendingStore.fetchPendingTasks(userId.value);
      await fetchUserBranch(userId.value);
      await fetchBranches();
    } else {
      console.error("❌ UID 不存在於資料庫，跳回登入頁");
      router.push({ path: "/", query: { expired: true } });
    }
  } catch (error) {
    console.error("❌ 查詢 User ID 失敗：", error);
    router.push({ path: "/", query: { expired: true } });
  }
};

const UserBranch = ref([]);

const fetchUserBranch = async (userId) => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/users/get_userBranch`, {
      params: { userId: userId },
    });
    if (response.data.success) {
      UserBranch.value = response.data.branches.map((b) => b.Cod_Cust.trim());
    }
  } catch (err) {
    console.error("❌ 授權門市載入失敗:", err);
  }
};
const getUserInfo = async () => {
  try {
    const token = localStorage.getItem("jwt_token");
    if (!token) {
      throw new Error("未登入 token 不存在");
    }

    const response = await axios.get(`${apiBaseUrl}/auth/get-UserInfo`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (response.data.success) {
      const user = response.data.user;

      username.value = user.username;
      return user;
    } else {
      console.error("❌ 取得使用者失敗:", response.data.message);
      return null;
    }
  } catch (err) {
    console.error("❌ 呼叫 /auth/get-UserInfo 失敗:", err.message);
    return null;
  }
};

const initLiff = async () => {
  try {
    await liff.init({ liffId: "**********-kq5G7DjN" });

    if (liff.isLoggedIn()) {
      // LINE登入
      loginMethod.value = "line";
      const profile = await liff.getProfile();
      user.value = {
        isLoggedIn: true,
        userId: profile.userId,
        displayName: profile.displayName,
        pictureUrl: profile.pictureUrl,
      };
      uid.value = profile.userId;

      fetchUserId();
    } else {
      // 帳號密碼登入
      loginMethod.value = "account";
      uid.value = username.value;
      fetchUserId();
    }
  } catch (error) {
    console.error("❌ LIFF 初始化失敗:", error);
    router.push("/");
  }
};

// 登出功能
const logoutLiff = () => {
  localStorage.removeItem("jwt_token");
  if (liff.isLoggedIn()) {
    liff.logout();
  }
  router.push("/");
};
let keepAliveTimer = null;
let lastActivityTime = Date.now();

//#KeepAlive
async function sendKeepAlive() {
  const now = Date.now();
  const diff = now - lastActivityTime;

  if (diff < 1 * 60 * 1000) {
    try {
      const token = localStorage.getItem("jwt_token");

      const response = await apiClient.post(
        `${apiBaseUrl}/auth/keep-Alive`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response?.data?.token) {
        localStorage.setItem("jwt_token", response.data.token);
        console.log("🔁 JWT 已自動更新");
      }
    } catch (err) {
      console.error("❌ keep-alive 失敗:", err.message);
    }
  }
}
// 記錄使用者有操作
const resetActivityTime = () => {
  lastActivityTime = Date.now();
};

// 添加處理公告通知點擊的方法
const handleOpenBulletins = (type, item) => {
  switch (type) {
    case "pending":
      activeContent.value = "formsign";
      // 如果有具體表單信息，可以傳遞給表單頁面
      if (item) {
        localStorage.setItem("pending_form", JSON.stringify(item));
      }
      break;
    case "required":
    case "unread":
      activeContent.value = "btinlist";
      localStorage.setItem("bulletin_filter", type);
      // 如果有具體公告信息，可以傳遞給公告列表頁面
      if (item) {
        localStorage.setItem("selected_bulletin", JSON.stringify(item));
      }
      nextTick(() => {
        window.dispatchEvent(
          new CustomEvent("filter-bulletins", {
            detail: { filter: type, item: item },
          })
        );
      });
      break;
  }
};

// 新增: 獲取用戶所有權限群組
const fetchUserGroups = async (userId) => {
  try {
    const response = await apiClient.get("/users/get_user_access", {
      params: { userId },
    });

    if (response.data.success) {
      // 保存用戶的所有權限群組的 value (即權限代碼)
      userGroups.value = response.data.access.map((group) => group.value);

      // 如果沒有獲取到任何群組，設置為空數組
      if (userGroups.value.length === 0) {
        userGroups.value = [];
        console.warn("用戶沒有任何權限群組");
      }
    } else {
      // API 呼叫失敗，設置為空數組
      console.error("無法獲取用戶權限群組");
      userGroups.value = [];
    }
  } catch (error) {
    console.error("❌ 獲取用戶權限群組失敗:", error);
    // 發生錯誤時，設置為空數組
    userGroups.value = [];

    // 提醒用戶重新登入
    $q.notify({
      type: "negative",
      message: "無法獲取權限信息，請重新登入",
      position: "top",
      timeout: 5000,
    });
  }
};

// 在組件載入時初始化 LIFF
onMounted(async () => {
  await getUserInfo();
  await initLiff();

  await pendingStore.fetchPendingTasks();
  await loadPagePermissions();
  await fetchFormsPage();
  if (uid.value) {
    console.log("🚀 初始 keepalive 發送");
    await apiClient.post(`${apiBaseUrl}/auth/keep-Alive`, {
      uid: uid.value,
    });
  }
  window.addEventListener("mousemove", resetActivityTime);
  window.addEventListener("keydown", resetActivityTime);
  window.addEventListener("click", resetActivityTime);
  window.addEventListener("scroll", resetActivityTime);

  // 每分鐘檢查是否要送 keepalive
  keepAliveTimer = setInterval(sendKeepAlive, 60 * 1000);
});

onBeforeUnmount(() => {
  clearInterval(keepAliveTimer);
  window.removeEventListener("mousemove", resetActivityTime);
  window.removeEventListener("keydown", resetActivityTime);
  window.removeEventListener("click", resetActivityTime);
  window.removeEventListener("scroll", resetActivityTime);
});

// 在 script setup 部分添加以下計算屬性
const firstLevelMenus = computed(() => {
  return allpageOptions.value.filter((m) => m.Slevel === 1);
});

const getSecondLevelMenus = (parentId) => {
  return allpageOptions.value.filter(
    (m) => m.Slevel === 2 && m.Parent_id?.trim() === parentId.trim()
  );
};

const getFilteredThirdLevelMenus = (parentId) => {
  return allpageOptions.value.filter(
    (m) =>
      m.Slevel === 3 && m.Parent_id === parentId && hasPermission(m.Page_id)
  );
};
</script>

<style scoped>
.q-header {
  background: #1976d2;
  color: white;
}
.q-toolbar-title {
  font-weight: bold;
}
/* ✅ Vue 3 scoped CSS 正確的寫法 */
::v-deep(.q-item__section--avatar) {
  min-width: 36px !important; /* ✅ 縮小間距 */
}
.menu-header {
  position: sticky;
  top: 0;
  z-index: 2;
  background: #795548;
}
.menu-list-scroll {
  overflow-y: auto;
  flex: 1 1 auto;
}

/* 圖標垂直置中 */
.icon-center {
  display: inline-flex;
  vertical-align: middle;
  align-items: center;
}
</style>
