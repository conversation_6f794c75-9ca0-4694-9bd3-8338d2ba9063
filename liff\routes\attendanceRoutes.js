const express = require("express");
const {
  getBranchs,
  downloadAttendance,
  getAttendance,
  syncDeviceTime,
  batchDownload,
  batchStatus,
  downloadAttendanceTxt,
  deleteAttendance,
  exportFtp,
  testFtp,
  deleteDeviceAtt,
} = require("../controllers/attendanceController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

// 🔹 取得門市清單
router.get("/get_Branchs", authMiddleware, getBranchs);
router.post("/download_Attendance", authMiddleware, downloadAttendance);
router.get("/get_Attendance", authMiddleware, getAttendance);
router.post("/sync_DeviceTime", authMiddleware, syncDeviceTime);
router.post("/batch_Download", authMiddleware, batchDownload);
router.get("/batch_Status", authMiddleware, batchStatus);
router.post("/download_AttendanceTxt", authMiddleware, downloadAttendanceTxt);
router.post("/delete_Attendance", authMiddleware, deleteAttendance);
router.get("/test_Ftp", authMiddleware, testFtp);
router.post("/export_Ftp", authMiddleware, exportFtp);
router.post("/delete_DeviceAtt", authMiddleware, deleteDeviceAtt);

module.exports = router;
