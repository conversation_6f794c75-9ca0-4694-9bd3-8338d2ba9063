const sql = require("mssql");
const ZKLib = require("zkteco-js");
const dbConfig = require("../config/db"); // ← 依你的設定調整
const { DateTime } = require("luxon");
const ftp = require("basic-ftp");
const path = require("path");
const fs = require("fs/promises");

// 🟢 檢查UTC後輸出+8
function getDeviceTime() {
  const localOffset = new Date().getTimezoneOffset(); // 單位為分鐘
  const isUTC = localOffset === 0;

  return isUTC
    ? DateTime.now().plus({ hours: 8 }).toJSDate() // Docker 時補 8 小時
    : DateTime.now().toJSDate(); // 本地測試直接送出
}

// 🟢 檢查UTC後輸出-8
function convertToUTCFromDevice(localTimeStr) {
  const localOffset = new Date().getTimezoneOffset(); // 單位為分鐘
  const isUTC = localOffset === 0;

  const dt = DateTime.fromJSDate(new Date(localTimeStr));
  if (!dt.isValid) return null;

  return isUTC
    ? dt.minus({ hours: 8 }).toJSDate() // Docker：打卡機是本地時間，要轉為 UTC
    : dt.toJSDate(); // 本地開發：打卡機也是本地時間，直接寫入
}

async function generateAttendanceTxt(startDate, endDate, stateMap) {
  const start = DateTime.fromISO(startDate).startOf("day").toJSDate();
  const end = DateTime.fromISO(endDate)
    .plus({ days: 1 })
    .startOf("day")
    .toJSDate();

  const pool = await sql.connect(dbConfig);
  const result = await pool
    .request()
    .input("start", sql.DateTime, start)
    .input("end", sql.DateTime, end).query(`
      SELECT R.User_id, R.Timestamp, R.State, B.AttendanceNo
      FROM AttendanceRecords R
      JOIN Branch B ON R.Cod_Cust = B.Cod_Cust
      WHERE R.Timestamp >= @start AND R.Timestamp < @end
      ORDER BY B.AttendanceNo, R.Timestamp
    `);

  return result.recordset
    .map((r) => {
      const t = DateTime.fromJSDate(r.Timestamp).setZone("Asia/Taipei");
      return [
        r.AttendanceNo,
        r.User_id.trim(),
        t.toFormat("yyyyLLdd"),
        t.toFormat("HHmm"),
        stateMap[r.State.trim()] || r.State.trim(),
      ].join(",");
    })
    .join("\r\n");
}

// 🟢 批次下載
async function downloadAndSaveAttendance(Cod_cust, AttendanceIP) {
  const zk = new ZKLib(AttendanceIP, 4370, 5200, 5000);
  let connected = false;
  let insertedCount = 0;

  try {
    const connectResult = await zk.createSocket();

    let connMessage = "";
    if (typeof connectResult === "string") {
      connMessage = connectResult;
    } else if (typeof connectResult === "object") {
      connMessage = connectResult.msg || connectResult.message || "";
    } else if (typeof connectResult === "boolean") {
      connMessage = connectResult
        ? "TCP connection successful"
        : "TCP connection failed";
    }

    if (!connMessage.includes("TCP connection successful")) {
      return { status: "error", message: "設備連線失敗" };
    }

    //console.log(Cod_cust + "測試回應:", connMessage);
    connected = true;

    // ✅ 同步設備時間
    const now = getDeviceTime();
    await zk.setTime(now);

    // ✅ 先取得設備資訊，檢查 logCounts
    const info = await zk.getInfo();
    const logCount = Number(info?.logCounts) || 0;
    //console.log(`${Cod_cust} 📟 考勤資料筆數：${logCount}`);

    if (logCount === 0) {
      return {
        status: "success",
        message: "設備連線成功，但目前無考勤紀錄",
        count: 0,
        deviceCount: 0,
      };
    }

    // ✅ 有資料才抓打卡紀錄
    const logs = await zk.getAttendances();
    const records = logs.data;

    if (!Array.isArray(records)) {
      return { status: "error", message: "資料格式錯誤" };
    }

    const pool = await sql.connect(dbConfig);

    for (const rec of records) {
      const userId = rec.user_id?.trim();
      if (!userId || !rec.record_time) continue;

      const rawTimestamp = convertToUTCFromDevice(rec.record_time);
      if (!rawTimestamp || isNaN(rawTimestamp)) continue;
      rawTimestamp.setSeconds(0, 0);

      try {
        const result = await pool.query`
          IF NOT EXISTS (
            SELECT 1 FROM AttendanceRecords
            WHERE Cod_Cust = ${Cod_cust}
              AND User_id = ${userId}
              AND Timestamp = ${rawTimestamp}
              AND State = ${rec.state}
          )
          BEGIN
            INSERT INTO AttendanceRecords (Cod_Cust, User_id, Timestamp, State, Type)
            VALUES (${Cod_cust}, ${userId}, ${rawTimestamp}, ${rec.state}, ${rec.type})
            SELECT 1 AS Inserted
          END
          ELSE
            SELECT 0 AS Inserted
        `;

        if (result.recordset?.[0]?.Inserted === 1) {
          insertedCount++;
        }
      } catch (err) {
        console.warn("⚠️ 匯入錯誤：", err.message);
      }
    }

    await pool.query`
      UPDATE Branch
      SET AttendanceSync = GETUTCDATE()
      WHERE Cod_Cust = ${Cod_cust}
    `;

    return { status: "success", count: insertedCount, deviceCount: logCount };
  } catch (err) {
    console.error("❌ 錯誤發生於 Cod_cust:", Cod_cust);
    console.error("❌ 錯誤訊息:", err);
    return { status: "error", message: err.message || "未知錯誤" };
  } finally {
    if (connected) {
      try {
        zk.disconnect();
      } catch (e) {
        console.warn("⚠️ 中斷連線失敗", e.message);
      }
    }
  }
}

// 🟢 讀取門市資料
const getBranchs = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);

    const result = await pool.request().query(`
      SELECT Cod_Cust, Cod_Name, AttendanceIP , AttendanceSync
      FROM branch
      WHERE Sts >= '1' AND AttendanceIP IS NOT NULL AND LTRIM(RTRIM(AttendanceIP)) <> ''
    `);

    res.json(result.recordset);
  } catch (err) {
    console.error("❌ getBranchs error:", err);
    res.status(500).json({ error: "伺服器錯誤" });
  }
};

// 🟢 下載設備紀錄到本機
const downloadAttendance = async (req, res) => {
  const { Cod_cust, AttendanceIP } = req.body;

  if (!Cod_cust || !AttendanceIP) {
    return res.status(400).json({ error: "缺少必要參數" });
  }

  const zk = new ZKLib(AttendanceIP, 4370, 5200, 5000);
  let connected = false;

  try {
    const connectResult = await zk.createSocket();
    let connMessage = "";
    let insertedCount = 0;

    if (typeof connectResult === "string") {
      connMessage = connectResult;
    } else if (typeof connectResult === "object") {
      connMessage = connectResult.msg || connectResult.message || "";
    } else if (typeof connectResult === "boolean") {
      connMessage = connectResult
        ? "TCP connection successful"
        : "TCP connection failed";
    }

    if (!connMessage.includes("TCP connection successful")) {
      return res.status(500).json({ error: "設備連線失敗" });
    }
    connected = true;

    const info = await zk.getInfo();
    const logCount = Number(info?.logCounts) || 0;
    console.log(`${Cod_cust} 📟 考勤資料筆數：${logCount}`);

    // ✅ 若無資料，直接結束
    if (logCount === 0) {
      return res.json({
        status: "success",
        message: "設備連線成功，但目前無考勤紀錄",
        count: 0,
        logCount: 0,
      });
    }

    // ✅ 抓取打卡紀錄
    const logs = await zk.getAttendances();
    const records = Array.isArray(logs?.data) ? logs.data : [];

    if (!Array.isArray(records)) {
      return { status: "error", message: "資料格式錯誤" };
    }

    const pool = await sql.connect(dbConfig);

    for (const rec of records) {
      const userId = rec.user_id?.trim();
      if (!userId || !rec.record_time) continue;

      const rawTimestamp = convertToUTCFromDevice(rec.record_time);
      if (!rawTimestamp || isNaN(rawTimestamp)) continue;
      rawTimestamp.setSeconds(0, 0);

      try {
        const result = await pool.query`
          IF NOT EXISTS (
            SELECT 1 FROM AttendanceRecords
            WHERE Cod_Cust = ${Cod_cust}
              AND User_id = ${userId}
              AND Timestamp = ${rawTimestamp}
              AND State = ${rec.state}
          )
          BEGIN
            INSERT INTO AttendanceRecords (Cod_Cust, User_id, Timestamp, State, Type)
            VALUES (${Cod_cust}, ${userId}, ${rawTimestamp}, ${rec.state}, ${rec.type})
            SELECT 1 AS Inserted
          END
          ELSE
            SELECT 0 AS Inserted
        `;

        if (result.recordset?.[0]?.Inserted === 1) {
          insertedCount++;
        }
      } catch (err) {
        console.warn("⚠️ 匯入錯誤：", err.message);
      }
    }

    await pool.query`
      UPDATE Branch
      SET AttendanceSync = GETUTCDATE()
      WHERE Cod_Cust = ${Cod_cust}
    `;

    res.json({
      status: "success",
      count: insertedCount,
      deviceCount: logCount,
    });
  } catch (err) {
    console.error("❌ 下載失敗:", err);
    res
      .status(500)
      .json({ error: "打卡紀錄下載失敗", detail: err.message || err });
  } finally {
    if (connected) {
      try {
        zk.disconnect();
      } catch (e) {
        console.warn("⚠️ 中斷連線時發生錯誤:", e.message);
      }
    }
  }
};

// 🟢 取得已下載紀錄
const getAttendance = async (req, res) => {
  const { Cod_cust } = req.query;
  if (!Cod_cust) return res.status(400).json({ error: "缺少 Cod_cust" });

  try {
    const pool = await sql.connect(dbConfig);
    const result = await pool.query`
      SELECT Cod_Cust, User_id, Timestamp, State, Type
      FROM AttendanceRecords
      WHERE Cod_Cust = ${Cod_cust}
      ORDER BY Timestamp DESC
    `;
    res.json(result.recordset);
  } catch (err) {
    console.error("❌ 查詢紀錄失敗:", err);
    res.status(500).json({ error: "查詢紀錄失敗" });
  }
};

// 🟢 同步設備時間
const syncDeviceTime = async (req, res) => {
  const { ip } = req.body;

  if (!ip) {
    return res.status(400).json({ error: "缺少 IP 參數" });
  }

  const zk = new ZKLib(ip, 4370, 5200, 5000);
  let connected = false;
  try {
    const connectResult = await zk.createSocket();
    let connMessage = "";

    if (typeof connectResult === "string") {
      connMessage = connectResult;
    } else if (typeof connectResult === "object") {
      connMessage = connectResult.msg || connectResult.message || "";
    } else if (typeof connectResult === "boolean") {
      connMessage = connectResult
        ? "TCP connection successful"
        : "TCP connection failed";
    }

    //console.log(" 測試回應:", connMessage);

    if (!connMessage.includes("TCP connection successful")) {
      return res.status(500).json({ error: "設備連線失敗" });
    }
    connected = true;

    const now = getDeviceTime();
    await zk.setTime(now);

    res.json({
      status: "success",
      message: `設備時間已同步`,
    });
  } catch (err) {
    console.error("❌ 設定時間錯誤:", err);
    res.status(500).json({ error: "設定設備時間失敗", detail: err.message });
  }
};

// 🟢 刪除設備紀錄
const deleteDeviceAtt = async (req, res) => {
  const { ip } = req.body;

  if (!ip) {
    return res.status(400).json({ error: "缺少 IP 參數" });
  }

  const zk = new ZKLib(ip, 4370, 5200, 5000);
  let connected = false;
  try {
    const connectResult = await zk.createSocket();
    let connMessage = "";

    if (typeof connectResult === "string") {
      connMessage = connectResult;
    } else if (typeof connectResult === "object") {
      connMessage = connectResult.msg || connectResult.message || "";
    } else if (typeof connectResult === "boolean") {
      connMessage = connectResult
        ? "TCP connection successful"
        : "TCP connection failed";
    }

    //console.log(" 測試回應:", connMessage);

    if (!connMessage.includes("TCP connection successful")) {
      return res.status(500).json({ error: "設備連線失敗" });
    }
    connected = true;

    await zk.clearAttendanceLog();

    res.json({
      status: "success",
      message: `設備紀錄已刪除`,
    });
  } catch (err) {
    console.error("❌ 刪除紀錄錯誤:", err);
    res.status(500).json({ error: "刪除設備紀錄失敗", detail: err.message });
  }
};

// 🟢 批次下載主迴圈
const batchDownload = async (req, res) => {
  const pool = await sql.connect(dbConfig);
  const result = await pool.query(`
    SELECT Cod_Cust, Cod_Name, AttendanceIP
    FROM Branch
    WHERE Sts >= '1'
      AND AttendanceIP IS NOT NULL
      AND LTRIM(RTRIM(AttendanceIP)) <> ''
  `);

  const branches = result.recordset;

  // 初始化進度
  global.batchProgress = branches.map((branch) => ({
    Cod_Cust: branch.Cod_Cust.trim(),
    Cod_Name: branch.Cod_Name.trim(),
    AttendanceIP: branch.AttendanceIP.trim(),
    status: "pending",
    message: "等待下載",
    count: 0,
  }));

  // 🟢 非同步執行每個下載任務
  branches.forEach(async (branch, index) => {
    try {
      const result = await downloadAndSaveAttendance(
        branch.Cod_Cust.trim(),
        branch.AttendanceIP.trim()
      );

      global.batchProgress[index].status = result.status;
      global.batchProgress[index].message =
        result.status === "success" ? "寫入成功" : "寫入失敗";
      global.batchProgress[index].count = result.count || 0;
      global.batchProgress[index].deviceCount = result.deviceCount || 0;
    } catch (err) {
      global.batchProgress[index].status = "error";
      global.batchProgress[index].message = err.message || "連線失敗";
      global.batchProgress[index].count = 0;
    }
  });

  // 馬上回應前端，開始執行了
  res.json({
    status: "started",
    message: `批次下載已啟動，共 ${branches.length} 門市`,
    total: branches.length,
  });
};

// 🟢 前端讀取狀態
const batchStatus = (req, res) => {
  res.json(global.batchProgress || []);
};

// 🟢 下載紀錄
const downloadAttendanceTxt = async (req, res) => {
  try {
    const { startDate, endDate, stateMap } = req.body;

    if (!startDate || !endDate || !stateMap) {
      return res.status(400).json({ error: "缺少參數" });
    }

    const output = await generateAttendanceTxt(startDate, endDate, stateMap); // ✅ 呼叫剛剛抽出的函式

    const filename = `Attendance_${DateTime.now().toFormat(
      "yyyyLLdd_HHmmss"
    )}.txt`;
    res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
    res.setHeader("Content-Type", "text/plain; charset=utf-8");
    res.send(output); // ✅ 傳送回瀏覽器下載
  } catch (err) {
    console.error("❌ 匯出 TXT 錯誤:", err);
    res.status(500).json({ error: "匯出失敗", detail: err.message });
  }
};

const deleteAttendance = async (req, res) => {
  const { beforeDate } = req.body;

  if (!beforeDate) {
    return res.status(400).json({ error: "缺少 beforeDate" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    const result = await pool
      .request()
      .input("beforeDate", sql.DateTime, new Date(beforeDate))
      .query("DELETE FROM AttendanceRecords WHERE Timestamp < @beforeDate");

    res.json({
      message: "刪除成功",
      rowsAffected: result.rowsAffected[0],
    });
  } catch (err) {
    console.error("❌ 刪除出錯：", err);
    res.status(500).json({ error: "伺服器錯誤", details: err.message });
  }
};

// 🟢 FTP測試連線
const testFtp = async (req, res) => {
  const { host, user, password } = req.query;
  console.log(host);
  if (!host || !user || !password) {
    return res.status(400).json({ status: "error", message: "缺少必要參數" });
  }

  const client = new ftp.Client();
  client.ftp.verbose = false;

  try {
    await client.access({
      host,
      user,
      password,
      secure: false, // 改成 true 若你使用 FTPS
    });

    res.json({ status: "success", message: "FTP 連線成功" });
  } catch (err) {
    res.status(500).json({
      status: "error",
      message: "FTP 連線失敗",
      detail: err.message,
    });
  } finally {
    client.close();
  }
};

const exportFtp = async (req, res) => {
  const {
    host,
    user,
    password,
    remoteDir = "/",
    startDate,
    endDate,
    stateMap,
  } = req.body;

  if (!host || !user || !password || !startDate || !endDate || !stateMap) {
    return res.status(400).json({ status: "error", message: "缺少必要參數" });
  }

  try {
    const outputRaw = await generateAttendanceTxt(startDate, endDate, stateMap);
    const output = outputRaw.replace(/\n/g, "\r\n"); // ✅ 強制換行為 Windows 標準格式

    const todayName = DateTime.now().toFormat("yyyyLLdd") + ".txt";
    const localPath = path.join(__dirname, todayName);

    // ✅ 寫入本地暫存檔案
    await fs.writeFile(localPath, output, "utf8");

    // ✅ 建立 FTP 連線
    const client = new ftp.Client();
    client.ftp.verbose = false;

    try {
      await client.access({ host, user, password, secure: false });
      await client.ensureDir(remoteDir);

      // ✅ 檢查並刪除舊檔案（若存在）
      const files = await client.list();
      const exists = files.some((f) => f.name === todayName);
      if (exists) {
        await client.remove(todayName);
      }

      // ✅ 上傳新檔案
      await client.uploadFrom(localPath, todayName);

      res.json({ status: "success", message: "FTP 上傳成功" });
    } catch (ftpErr) {
      res.status(500).json({
        status: "error",
        message: "❌ FTP 上傳失敗",
        detail: ftpErr.message,
      });
    } finally {
      client.close();
      // ✅ 刪除本地暫存檔
      try {
        await fs.unlink(localPath);
      } catch (e) {
        console.warn("⚠️ 刪除暫存檔失敗:", e.message);
      }
    }
  } catch (err) {
    console.error("❌ 錯誤：", err);
    res.status(500).json({
      status: "error",
      message: "❌ 檔案產生失敗",
      detail: err.message,
    });
  }
};

module.exports = {
  downloadAndSaveAttendance,
  getBranchs,
  downloadAttendance,
  getAttendance,
  syncDeviceTime,
  batchDownload,
  batchStatus,
  downloadAttendanceTxt,
  deleteAttendance,
  testFtp,
  exportFtp,
  deleteDeviceAtt,
};
