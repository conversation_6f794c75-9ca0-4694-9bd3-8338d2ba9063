<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto; position: relative"
  >
    <!-- 標題和統計卡片容器 -->
    <div class="title-stats-container">
      <q-card
        class="q-pa-xs no-shadow"
        style="width: 100%; border-radius: 10px"
      >
        <!-- 標題與搜尋按鈕 -->
        <q-card-section class="title-section q-py-xs">
          <div class="row items-center justify-between">
            <div class="text-subtitle1 text-primary">公告管理</div>
            <div class="row">
              <q-btn
                flat
                round
                size="sm"
                color="primary"
                class="search-button q-mr-xs"
                @click="openBulletinDialog()"
              >
                <circle-plus :size="18" :stroke-width="1.5" />
              </q-btn>
              <q-btn
                flat
                round
                size="sm"
                color="red"
                class="search-button"
                @click="openSearchDialog"
              >
                <Search :size="18" :stroke-width="1.5" />
                <q-tooltip>搜尋公告</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-card-section>

        <!-- 公告統計卡片 -->
        <q-card-section class="q-pt-none">
          <div class="stats-container">
            <div class="row q-col-gutter-sm justify-center">
              <!-- 全部公告統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card all-card"
                  :class="{ 'active-filter': activeFilter === 'all' }"
                  @click="filterBulletins('all')"
                >
                  <div class="stats-icon">
                    <message-square-text :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ bulletins.length }}</div>
                    <div class="stats-label">全部公告</div>
                  </div>
                </div>
              </div>

              <!-- 啟用公告統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card active-card"
                  :class="{ 'active-filter': activeFilter === 'active' }"
                  @click="filterBulletins('active')"
                >
                  <div class="stats-icon">
                    <circle-check-big :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ activeCount }}</div>
                    <div class="stats-label">啟用公告</div>
                  </div>
                </div>
              </div>

              <!-- 停用公告統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card inactive-card"
                  :class="{ 'active-filter': activeFilter === 'inactive' }"
                  @click="filterBulletins('inactive')"
                >
                  <div class="stats-icon">
                    <circle-minus :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ inactiveCount }}</div>
                    <div class="stats-label">停用公告</div>
                  </div>
                </div>
              </div>

              <!-- 過期公告統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card expired-card"
                  :class="{ 'active-filter': activeFilter === 'expired' }"
                  @click="filterBulletins('expired')"
                >
                  <div class="stats-icon">
                    <circle-alert :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ expiredCount }}</div>
                    <div class="stats-label">過期公告</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>

        <!-- 新增公告按鈕 -->

        <!-- 添加分頁控制器到固定區域 -->
        <q-card-section class="q-pt-none q-pb-xs">
          <div class="row justify-end">
            <q-pagination
              v-if="filteredBulletins.length > 0"
              v-model="currentPage"
              :max="totalPages"
              :max-pages="5"
              boundary-numbers
              direction-links
              color="primary"
              active-color="blue"
              active-text-color="white"
              size="sm"
              class="pagination-controls"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <!-- 公告列表 -->
      <q-card-section class="q-pa-xs">
        <div
          class="text-subtitle2 text-primary flex items-center q-mb-sm q-px-sm"
        >
          <list :stroke-width="1" :size="22" class="q-mr-sm" />
          公告列表
        </div>

        <div v-if="loading.table" class="loader-container">
          <q-spinner size="40px" color="grey-5" />
          <div class="text-subtitle1 q-mt-sm q-pl-md text-grey-5">
            載入公告中...
          </div>
        </div>
        <div
          v-else-if="filteredBulletins.length === 0"
          class="empty-state-container"
        >
          <div class="empty-state q-pa-md flex flex-center column text-grey-5">
            <svg
              class="empty-icon"
              viewBox="0 0 24 24"
              style="width: 60px; height: 50px; margin-bottom: 12px"
            >
              <path
                d="M20,2H4C2.9,2,2,2.9,2,4v18l4-4h14c1.1,0,2-0.9,2-2V4C22,2.9,21.1,2,20,2z M20,16H5.2L4,17.2V4h16V16z"
              />
              <path d="M11,12h2v2h-2V12z" />
              <path d="M11,6h2v4h-2V6z" />
            </svg>
            <div class="empty-text text-grey-5">
              {{
                searchForm.title
                  ? "沒有找到符合的公告"
                  : activeFilter
                  ? "沒有符合條件的公告"
                  : "請選擇過濾條件或搜尋公告"
              }}
            </div>
          </div>
        </div>
        <q-list separator v-else class="bulletin-list">
          <div
            v-for="bulletin in paginatedBulletins"
            :key="bulletin.id"
            class="bg-white q-mb-xs"
            style="
              border: 1px solid #eee;
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            "
          >
            <q-expansion-item
              expand-separator
              class="q-pa-none"
              header-class="text-indigo bulletin-header"
            >
              <!-- 公告主列 -->
              <template #header>
                <q-item
                  dense
                  class="q-pa-none q-gutter-none items-center q-px-none"
                  style="width: 100%; position: relative"
                >
                  <!-- 複製按鈕 -->
                  <q-btn
                    flat
                    round
                    :color="
                      canManageBulletin(bulletin) ? 'secondary' : 'grey-5'
                    "
                    @click.stop="duplicateBulletin(bulletin)"
                    :disable="!canManageBulletin(bulletin)"
                    class="q-mr-sm"
                  >
                    <copy :stroke-width="1" :size="20" />
                    <q-tooltip>複製此公告</q-tooltip>
                  </q-btn>

                  <!-- 公告資訊 -->
                  <q-item-section>
                    <!-- 狀態標記 - 固定在最上面一列 -->
                    <div class="row items-center q-mb-xs">
                      <q-badge
                        v-if="bulletin.active === 0"
                        color="brown"
                        text-color="white"
                        label="停用"
                        class="q-mr-sm bulletin-badge"
                        outline
                      />
                      <q-badge
                        v-if="bulletin.priority === 1"
                        color="negative"
                        text-color="white"
                        label="急件"
                        class="q-mr-sm bulletin-badge"
                        outline
                      />
                      <q-badge
                        v-if="bulletin.required === 1"
                        color="orange"
                        text-color="white"
                        label="必讀"
                        class="q-mr-sm bulletin-badge"
                        outline
                      />
                      <q-badge
                        v-if="bulletin.event_id"
                        color="primary"
                        text-color="white"
                        label="行事曆"
                        class="q-mr-sm bulletin-badge"
                        outline
                      />
                      <q-badge
                        v-if="isExpired(bulletin)"
                        color="grey"
                        text-color="white"
                        label="已過期"
                        class="q-mr-sm bulletin-badge"
                        outline
                      />
                    </div>

                    <!-- 標題 -->
                    <div class="bulletin-title q-mb-xs">
                      【{{ getDepartmentName(bulletin.dep) }}】{{
                        bulletin.title
                      }}
                    </div>

                    <div
                      class="text-caption text-grey-8 bulletin-meta flex items-center"
                    >
                      <calendar-fold
                        :size="14"
                        :stroke-width="1"
                        class="q-mr-xs"
                      />
                      {{ new Date(bulletin.publish_at).toLocaleDateString() }}
                      <template v-if="bulletin.expires_at">
                        {{
                          " - " +
                          new Date(bulletin.expires_at).toLocaleDateString()
                        }}
                      </template>
                    </div>
                  </q-item-section>
                  <!-- 最右側展開箭頭 -->
                  <q-item-section side />
                  <!-- 編輯按鈕 -->
                  <q-btn
                    v-if="canManageBulletin(bulletin)"
                    flat
                    round
                    dense
                    unelevated
                    color="brown"
                    @click.stop="openBulletinDialog(bulletin)"
                  >
                    <template #default>
                      <square-pen :size="18" :stroke-width="1" />
                    </template>
                  </q-btn>
                </q-item>
              </template>
              <!-- 詳細內容 -->
              <q-card-section class="q-pa-sm q-pt-sm bg-grey-1">
                <!-- 基本資訊 -->
                <div class="row q-col-gutter-xs q-mb-sm">
                  <div class="col-12">
                    <div
                      class="bg-white q-pa-sm rounded-borders content-container"
                      v-html="bulletin.contents"
                    ></div>
                  </div>
                </div>

                <!-- 附件列表 -->
                <div
                  v-if="bulletin.attachments && bulletin.attachments.length"
                  class="q-mb-sm"
                >
                  <q-card flat bordered>
                    <q-card-section class="q-py-xs">
                      <div
                        class="row items-center text-subtitle2 text-primary q-mb-xs"
                      >
                        <q-icon name="attach_file" class="q-mr-xs" size="xs" />
                        附件列表
                      </div>
                      <q-list bordered separator>
                        <q-item
                          v-for="attachment in bulletin.attachments"
                          :key="attachment.id"
                          class="q-pa-xs"
                        >
                          <q-item-section>
                            <div
                              class="text-subtitle2 cursor-pointer text-primary attachment-link"
                              style="text-decoration: underline"
                              @click="previewAttachment(attachment)"
                            >
                              附件 {{ attachment.sno }}
                            </div>
                          </q-item-section>
                          <q-item-section side>
                            <q-btn
                              flat
                              round
                              dense
                              icon="download"
                              color="primary"
                              size="sm"
                              @click="downloadAttachment(attachment)"
                            />
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </q-card-section>
                  </q-card>
                </div>

                <!-- 操作按鈕 -->
                <div class="row justify-end q-gutter-sm q-mt-sm">
                  <q-btn
                    v-if="canManageBulletin(bulletin)"
                    flat
                    size="12px"
                    color="indigo"
                    @click.stop="openStatsDialog(bulletin)"
                  >
                    <file-chart-column-increasing
                      :stroke-width="1.2"
                      :size="26"
                    />閱讀統計
                  </q-btn>
                  <q-btn
                    class="q-ml-sm"
                    v-if="canManageBulletin(bulletin)"
                    flat
                    size="12px"
                    color="red"
                    @click.stop="confirmDelete(bulletin)"
                  >
                    <file-x :stroke-width="1" :size="26" />刪除公告
                  </q-btn>
                </div>
              </q-card-section>
            </q-expansion-item>
          </div>
        </q-list>
      </q-card-section>
    </q-card>

    <!-- 新增/編輯公告對話框 -->
    <q-dialog v-model="bulletinDialog.show" persistent>
      <q-card
        style="
          width: 100%;
          max-width: 800px;
          max-height: 90vh;
          display: flex;
          flex-direction: column;
          border-radius: 12px;
        "
      >
        <!-- 固定標題 -->
        <q-card-section
          class="row items-center justify-between bg-primary text-white q-pb-sm"
          style="
            position: sticky;
            top: 0;
            z-index: 2;
            border-radius: 12px 12px 0 0;
          "
        >
          <div class="text-h6">
            <q-icon name="article" class="q-mr-sm" />
            {{ bulletinDialog.isEdit ? "編輯公告" : "新增公告" }}
          </div>
          <q-btn
            icon="close"
            flat
            round
            dense
            v-close-popup
            class="text-white"
          />
        </q-card-section>

        <q-separator />

        <!-- 可滾動的內容區域 -->
        <q-card-section class="scroll" style="flex: 1; overflow-y: auto">
          <q-form @submit="saveBulletin">
            <!-- 基本資訊 -->
            <div class="q-mb-md">
              <div
                class="text-subtitle1 text-primary q-mb-sm row items-center justify-between"
              >
                <div>
                  <q-icon name="info" class="q-mr-xs" />
                  基本資訊
                </div>
                <div class="row items-center">
                  <span class="text-caption q-mr-xs">必讀</span>
                  <q-toggle
                    v-model="bulletinForm.required"
                    color="negative"
                    :true-value="1"
                    :false-value="0"
                    dense
                    class="q-mr-md"
                  />
                  <span class="text-caption q-mr-xs">E-mail通知</span>
                  <q-toggle
                    v-model="bulletinForm.notify"
                    color="blue"
                    :true-value="1"
                    :false-value="0"
                    dense
                    class="q-mr-md"
                    :disable="bulletinDialog.isEdit"
                  />
                  <span class="text-caption q-mr-xs">啟用</span>
                  <q-toggle
                    v-model="bulletinForm.active"
                    color="green"
                    :true-value="1"
                    :false-value="0"
                    dense
                  />
                </div>
              </div>

              <div class="row q-col-gutter-md">
                <div class="col-12">
                  <q-input
                    v-model="bulletinForm.title"
                    label="公告標題"
                    outlined
                    dense
                    :rules="[(val) => !!val || '標題為必填項']"
                  >
                    <template v-slot:prepend>
                      <q-icon name="title" color="primary" />
                    </template>
                  </q-input>
                </div>

                <div class="col-12 col-md-6">
                  <q-select
                    v-model="bulletinForm.dep"
                    :options="depOptions"
                    label="發布單位"
                    outlined
                    dense
                    :rules="[(val) => !!val || '發布單位為必填項']"
                    emit-value
                    map-options
                    :readonly="!isAdmin"
                  >
                    <template v-slot:prepend>
                      <q-icon name="business" color="primary" />
                    </template>
                  </q-select>
                </div>

                <div class="col-12 col-md-6">
                  <q-input
                    v-model="bulletinForm.publish_at"
                    label="發布日期"
                    outlined
                    dense
                    mask="####-##-##"
                    placeholder="YYYY-MM-DD"
                  >
                    <template v-slot:prepend>
                      <q-icon name="event" color="primary" />
                    </template>
                    <template v-slot:append>
                      <q-icon name="event" class="cursor-pointer">
                        <q-popup-proxy
                          cover
                          transition-show="scale"
                          transition-hide="scale"
                        >
                          <q-date
                            v-model="bulletinForm.publish_at"
                            mask="YYYY-MM-DD"
                          >
                            <div class="row items-center justify-end">
                              <q-btn
                                v-close-popup
                                label="確定"
                                color="primary"
                                flat
                              />
                            </div>
                          </q-date>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                  </q-input>
                </div>

                <div class="col-12 col-md-6">
                  <q-input
                    v-model="bulletinForm.expires_at"
                    :label="
                      bulletinForm.add_to_calendar
                        ? '到期日期 (必填)'
                        : '到期日期 (可選)'
                    "
                    outlined
                    dense
                    clearable
                    mask="####-##-##"
                    placeholder="YYYY-MM-DD"
                  >
                    <template v-slot:prepend>
                      <q-icon name="event_busy" color="primary" />
                    </template>
                    <template v-slot:append>
                      <q-icon name="event" class="cursor-pointer">
                        <q-popup-proxy
                          cover
                          transition-show="scale"
                          transition-hide="scale"
                        >
                          <q-date
                            v-model="bulletinForm.expires_at"
                            mask="YYYY-MM-DD"
                          >
                            <div class="row items-center justify-end">
                              <q-btn
                                v-close-popup
                                label="確定"
                                color="primary"
                                flat
                              />
                            </div>
                          </q-date>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                  </q-input>
                </div>

                <div class="col-12 col-md-6">
                  <div class="row items-center q-py-sm">
                    <div class="col-4">優先等級</div>
                    <div class="col-8">
                      <q-rating
                        v-model="priorityRating"
                        :max="5"
                        size="1.5em"
                        color="amber"
                        icon="star_border"
                        icon-selected="star"
                        :icon-half="undefined"
                        no-dimming
                      />
                    </div>
                  </div>
                </div>

                <!-- 行事曆設定 -->
                <div class="col-12">
                  <q-separator class="q-my-md" />
                  <div class="row items-center justify-between q-mb-sm">
                    <div class="text-subtitle1 text-primary">
                      <q-icon name="event" class="q-mr-xs" />
                      行事曆設定
                    </div>
                    <q-toggle
                      v-model="bulletinForm.add_to_calendar"
                      color="positive"
                      :disable="!canEditCalendar"
                    >
                      <q-tooltip v-if="!canEditCalendar">
                        已過期或停用的公告無法編輯行事曆設定
                      </q-tooltip>
                    </q-toggle>
                  </div>

                  <!-- 行事曆時間設定 -->
                  <div
                    v-if="bulletinForm.add_to_calendar"
                    class="calendar-settings"
                  >
                    <div class="row q-col-gutter-md q-mb-md">
                      <div class="col-12">
                        <q-checkbox
                          v-model="bulletinForm.calendar_all_day"
                          label="全天事件"
                          color="primary"
                        />
                      </div>
                    </div>

                    <div
                      class="row q-col-gutter-md"
                      v-if="!bulletinForm.calendar_all_day"
                    >
                      <div class="col-12 col-md-6">
                        <div class="text-caption q-mb-sm text-grey-8">
                          開始時間
                        </div>
                        <q-input
                          v-model="bulletinForm.calendar_start_time"
                          outlined
                          dense
                          type="time"
                          class="event-input"
                        />
                      </div>
                      <div class="col-12 col-md-6">
                        <div class="text-caption q-mb-sm text-grey-8">
                          結束時間
                        </div>
                        <q-input
                          v-model="bulletinForm.calendar_end_time"
                          outlined
                          dense
                          type="time"
                          class="event-input"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 公告內容 -->
            <div class="q-mb-md">
              <div class="text-subtitle1 text-primary q-mb-sm">
                <q-icon name="description" class="q-mr-xs" />
                公告內容
              </div>

              <TiptapEditor
                v-model="bulletinForm.contents"
                placeholder="請輸入公告內容..."
                min-height="200px"
                @image-deleted="handleImageDeleted"
              />
              <div class="text-caption q-mt-xs q-mb-xs text-grey-6">
                <q-icon name="info" size="xs" class="q-mr-xs" />
                <span
                  >單一圖檔上限5MB，如超過請先上傳至其他空間再以連結方式插入圖檔</span
                >
              </div>
              <div
                v-if="!bulletinForm.contents"
                class="text-negative text-caption q-mt-xs"
              >
                公告內容為必填項
              </div>
            </div>

            <!-- 目標對象設定區 -->
            <div class="q-mb-md">
              <div class="row items-center justify-between q-mb-sm">
                <div class="text-subtitle1 text-primary">
                  <q-icon name="people" class="q-mr-xs" />
                  目標對象設定
                </div>
                <q-btn
                  color="secondary"
                  icon="add"
                  label="新增對象"
                  flat
                  dense
                  @click="addTarget"
                />
              </div>

              <div
                v-for="(target, index) in bulletinForm.targets"
                :key="index"
                class="q-mb-sm bg-grey-1 rounded-borders"
              >
                <div class="q-pa-sm">
                  <div class="row q-col-gutter-sm items-center">
                    <div class="col-4">
                      <q-select
                        v-model="target.target_type"
                        :options="targetTypeOptions"
                        label="類型"
                        dense
                        emit-value
                        map-options
                        @update:model-value="
                          (val) => handleTargetTypeChange(val, index)
                        "
                        :disable="target.isExisting"
                      />
                    </div>
                    <div class="col-3">
                      <q-btn
                        flat
                        outline
                        class="full-width"
                        color="primary"
                        @click="openSelectionDialog(target)"
                      >
                        <div class="row items-center no-wrap">
                          <q-icon name="people" class="q-mr-sm" />
                          <div>{{ getSelectedCountText(target) }}</div>
                        </div>
                      </q-btn>
                    </div>
                    <div class="col-4">
                      <q-select
                        v-model="target.permission_type"
                        :options="permissionTypeOptions"
                        label="權限"
                        dense
                        emit-value
                        map-options
                        :disable="target.isExisting"
                      />
                    </div>
                    <div class="col-1 flex justify-center">
                      <q-btn
                        flat
                        round
                        dense
                        icon="delete"
                        color="negative"
                        @click="removeTarget(index)"
                        :disable="target.isExisting"
                        :title="
                          target.isExisting
                            ? '已存在的目標對象不能刪除'
                            : '刪除此目標對象'
                        "
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div
                v-if="bulletinForm.targets.length === 0"
                class="text-center q-pa-md bg-grey-1 rounded-borders"
              >
                <q-icon name="info" color="grey" size="24px" />
                <div class="text-grey q-mt-sm">尚未設定目標對象</div>
              </div>
            </div>

            <!-- 附件上傳區 -->
            <div class="q-mb-md">
              <div class="text-subtitle1 text-primary q-mb-sm">
                <q-icon name="attach_file" class="q-mr-xs" />
                附件管理
              </div>

              <div class="row q-col-gutter-sm">
                <div class="col-12">
                  <q-file
                    v-model="fileToUpload"
                    label="選擇檔案"
                    outlined
                    dense
                    clearable
                    counter
                    multiple
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif"
                    @update:model-value="handleFileSelected"
                    class="q-mb-md"
                    hint="支援的檔案類型: PDF、Office文件、文字檔、圖片 (大小限制: 5MB)"
                  >
                    <template v-slot:prepend>
                      <q-icon name="attach_file" color="primary" />
                    </template>
                    <template v-slot:after>
                      <q-btn
                        round
                        dense
                        flat
                        icon="cloud_upload"
                        color="primary"
                        @click="uploadFile"
                        :disable="!fileToUpload"
                      />
                    </template>
                  </q-file>
                </div>
              </div>

              <q-list
                v-if="
                  bulletinForm.attachments &&
                  bulletinForm.attachments.length > 0
                "
                class="bg-grey-1 rounded-borders"
                separator
              >
                <q-item
                  v-for="attachment in bulletinForm.attachments"
                  :key="`${attachment.id}_${attachment.sno}`"
                  class="q-pa-sm"
                >
                  <q-item-section avatar>
                    <q-icon name="insert_drive_file" color="primary" />
                  </q-item-section>
                  <q-item-section
                    class="cursor-pointer"
                    @click="previewAttachment(attachment)"
                  >
                    <q-item-label
                      class="text-primary"
                      style="text-decoration: underline"
                      >附件 {{ attachment.sno }}.{{
                        attachment.file_type
                      }}</q-item-label
                    >
                  </q-item-section>
                  <q-item-section side>
                    <q-btn
                      flat
                      round
                      dense
                      icon="delete"
                      color="negative"
                      @click="deleteAttachment(attachment)"
                    />
                  </q-item-section>
                </q-item>
              </q-list>

              <div v-else class="text-center q-pa-md bg-grey-1 rounded-borders">
                <q-icon name="cloud_upload" color="grey" size="24px" />
                <div class="text-grey q-mt-sm">尚未上傳附件</div>
              </div>
            </div>
          </q-form>
        </q-card-section>

        <!-- 固定在底部的儲存按鈕 -->
        <q-card-actions
          align="right"
          class="bg-white q-py-md"
          style="position: sticky; bottom: 0; z-index: 2"
        >
          <q-btn
            type="submit"
            class="full-width"
            unelevated
            outline
            label="儲存"
            color="primary"
            :loading="loading.save"
            icon="save"
            size="md"
            @click="saveBulletin"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 閱讀統計對話框 -->
    <q-dialog v-model="statsDialog.show">
      <q-card
        style="
          width: 700px;
          max-width: 90vw;
          border-radius: 12px;
          display: flex;
          flex-direction: column;
          max-height: 90vh;
        "
      >
        <q-card-section
          class="row items-center bg-primary text-white q-pb-sm"
          style="
            border-radius: 12px 12px 0 0;
            position: sticky;
            top: 0;
            z-index: 2;
          "
        >
          <div class="text-h6">
            <q-icon name="bar_chart" class="q-mr-sm" />
            閱讀統計
          </div>
          <q-space />
          <q-btn
            icon="close"
            flat
            round
            dense
            v-close-popup
            class="text-white"
          />
        </q-card-section>

        <q-card-section
          class="q-pa-md scroll"
          style="overflow-y: auto; flex: 1"
        >
          <template v-if="statsDialog.data">
            <div class="text-h6 text-primary text-center q-mb-md">
              {{ statsDialog.data.bulletin.title }}
            </div>

            <q-card flat bordered class="q-mb-lg statistics-card">
              <q-card-section>
                <!-- 統計數據橫向排列，無卡片外框 -->
                <div
                  class="row q-col-gutter-sm justify-center items-stretch q-mb-md"
                >
                  <!-- 總目標人數 -->
                  <div class="col-4 flex flex-center">
                    <div class="flex column items-center">
                      <q-icon name="people" color="grey-7" size="28px" />
                      <div class="stats-value text-dark">
                        {{ statsDialog.data.targetUserCount }}
                      </div>
                      <div class="stats-label">總目標人數</div>
                    </div>
                  </div>
                  <!-- 已閱讀人數 -->
                  <div class="col-4 flex flex-center">
                    <div class="flex column items-center">
                      <q-icon
                        name="check_circle"
                        color="positive"
                        size="28px"
                      />
                      <div class="stats-value text-positive">
                        {{ statsDialog.data.readUserCount }}
                      </div>
                      <div class="stats-label">已閱讀人數</div>
                    </div>
                  </div>
                  <!-- 未閱讀人數 -->
                  <div class="col-4 flex flex-center">
                    <div class="flex column items-center">
                      <q-icon name="cancel" color="negative" size="28px" />
                      <div class="stats-value text-negative">
                        {{
                          statsDialog.data.targetUserCount -
                          statsDialog.data.readUserCount
                        }}
                      </div>
                      <div class="stats-label">未閱讀人數</div>
                    </div>
                  </div>
                </div>
                <!-- 下方顯示大的閱讀率圓環 -->
                <div class="row justify-center q-mt-md">
                  <q-circular-progress
                    :value="statsDialog.data.readPercentage"
                    size="140px"
                    :thickness="0.22"
                    color="primary"
                    class="q-ma-md"
                    show-value
                    font-size="0px"
                    track-color="grey-2"
                  >
                    <div class="text-center progress-content">
                      <div class="text-h4 text-weight-bold text-primary">
                        {{ statsDialog.data.readPercentage }}%
                      </div>
                      <div class="text-caption q-mt-xs">閱讀率</div>
                    </div>
                  </q-circular-progress>
                </div>
              </q-card-section>
            </q-card>

            <div class="q-mt-md">
              <div class="text-subtitle1 text-primary q-mb-sm">
                <q-icon name="list_alt" class="q-mr-xs" />
                閱讀明細
              </div>
              <q-table
                :rows="statsDialog.data.allUsers"
                :columns="statsColumns"
                row-key="user_id"
                dense
                bordered
                flat
                v-model:pagination="statsPagination"
                class="bg-white"
              >
                <template v-slot:header="props">
                  <q-tr :props="props">
                    <q-th
                      v-for="col in props.cols"
                      :key="col.name"
                      :props="props"
                      class="bg-primary text-white"
                    >
                      {{ col.label }}
                    </q-th>
                  </q-tr>
                </template>
                <template v-slot:body="props">
                  <q-tr :props="props">
                    <q-td key="name" :props="props">
                      {{ props.row.name }}
                    </q-td>
                    <q-td key="read_status" :props="props">
                      <q-badge
                        :color="
                          props.row.read_status === null
                            ? 'grey'
                            : props.row.read_status === 1
                            ? 'positive'
                            : 'blue'
                        "
                        :label="
                          props.row.read_status === null
                            ? '未讀'
                            : props.row.read_status === 1
                            ? '確認'
                            : '已讀'
                        "
                        outline
                      />
                    </q-td>
                    <q-td key="read_at" :props="props" class="text-center">
                      {{
                        props.row.read_at
                          ? new Date(props.row.read_at)
                              .toLocaleString("zh-TW", {
                                year: "numeric",
                                month: "2-digit",
                                day: "2-digit",
                                hour: "2-digit",
                                minute: "2-digit",
                                hour12: false,
                              })
                              .replace(/\//g, "-")
                          : "-"
                      }}
                    </q-td>
                  </q-tr>
                </template>
              </q-table>
            </div>
          </template>
          <div v-else class="text-center q-pa-xl">
            <q-spinner color="primary" size="3em" />
            <div class="q-mt-sm">載入中...</div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 搜尋對話框 -->
    <q-dialog v-model="searchDialog.show">
      <q-card style="width: 500px; max-width: 90vw; border-radius: 12px">
        <q-card-section
          class="row items-center bg-primary text-white q-pb-sm"
          style="border-radius: 12px 12px 0 0"
        >
          <div class="text-h6">
            <q-icon name="search" class="q-mr-sm" />
            搜尋公告
          </div>
          <q-space />
          <q-btn
            icon="close"
            flat
            round
            dense
            v-close-popup
            class="text-white"
          />
        </q-card-section>

        <q-card-section class="q-pt-md">
          <q-form @submit.prevent="searchBulletins">
            <!-- 主要搜尋條件區域 -->
            <div class="q-pa-sm">
              <!-- 公告標題 -->
              <q-input
                v-model="searchForm.title"
                label="公告標題"
                outlined
                dense
                clearable
                class="q-mb-md"
              >
                <template #prepend>
                  <q-icon name="title" color="primary" />
                </template>
              </q-input>

              <!-- 發布單位 -->
              <q-select
                v-model="searchForm.dep"
                :options="depOptions"
                label="發布單位"
                outlined
                dense
                clearable
                emit-value
                map-options
                class="q-mb-md"
              >
                <template #prepend>
                  <q-icon name="business" color="primary" />
                </template>
              </q-select>

              <!-- 發布日期範圍 -->

              <q-input
                v-model="searchForm.startDate"
                label="開始日期"
                outlined
                dense
                clearable
                mask="####-##-##"
                placeholder="YYYY-MM-DD"
              >
                <template #prepend>
                  <q-icon name="event" color="primary" />
                </template>
                <template #append>
                  <q-icon name="event" class="cursor-pointer">
                    <q-popup-proxy
                      cover
                      transition-show="scale"
                      transition-hide="scale"
                    >
                      <q-date v-model="searchForm.startDate" mask="YYYY-MM-DD">
                        <div class="row items-center justify-end">
                          <q-btn
                            v-close-popup
                            label="確定"
                            color="primary"
                            flat
                          />
                        </div>
                      </q-date>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>
            </div>

            <!-- 更多搜尋條件 (可折疊) -->

            <!-- 按鈕區域 -->
            <div class="row justify-end q-mt-md q-gutter-sm">
              <q-btn
                outline
                label="重置"
                color="grey"
                @click="clearFilter"
                icon="refresh"
              />
              <q-btn
                unelevated
                label="搜尋"
                color="primary"
                icon="search"
                @click="searchBulletins"
              />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 附件預覽對話框 -->
    <q-dialog v-model="previewDialog.show">
      <q-card style="width: 90vw; max-width: 900px; max-height: 90vh">
        <q-card-section class="row items-center bg-primary text-white">
          <div class="text-h6">
            <q-icon name="visibility" class="q-mr-sm" />
            附件預覽
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pa-none" style="height: 80vh">
          <div
            v-if="previewDialog.loading"
            class="full-height flex flex-center"
          >
            <q-spinner-dots color="primary" size="40px" />
            <div class="text-grey q-mt-sm q-ml-sm">載入中...</div>
          </div>
          <div
            v-else-if="previewDialog.error"
            class="full-height flex flex-center column"
          >
            <q-icon name="error" color="negative" size="40px" />
            <div class="text-negative q-mt-sm">{{ previewDialog.error }}</div>
            <q-btn
              flat
              color="primary"
              label="下載檔案"
              icon="download"
              class="q-mt-md"
              @click="downloadAttachment(previewDialog.attachment)"
            />
          </div>
          <iframe
            v-else
            :src="previewDialog.url"
            style="width: 100%; height: 100%; border: none"
          ></iframe>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 使用者選擇對話框 -->
    <q-dialog v-model="selectionDialog.show" persistent>
      <q-card style="width: 700px; max-width: 90vw">
        <q-card-section class="row items-center bg-primary text-white">
          <div class="text-h6">
            <q-icon name="people" class="q-mr-sm" />
            {{ selectionDialog.title }}
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <q-input
            v-model="selectionDialog.searchQuery"
            label="搜尋對象"
            outlined
            dense
            clearable
          >
            <template v-slot:append>
              <q-btn
                flat
                dense
                icon="select_all"
                color="primary"
                size="sm"
                @click="selectAllVisibleItems"
                :disable="!filteredItems.length"
                class="q-mr-xs"
              />
            </template>
          </q-input>
        </q-card-section>

        <q-card-section style="max-height: 50vh" class="scroll">
          <q-list separator>
            <q-item
              v-for="item in filteredItems"
              :key="item.id"
              clickable
              v-ripple
              @click="
                () => {
                  // 檢查是否為已選對象（來自初始數據）
                  const isInitialSelected =
                    selectionDialog.initialSelectedItems.includes(item.id);
                  const index = selectionDialog.selectedItems.indexOf(item.id);

                  // 如果是初始已選對象，則不允許取消選擇
                  if (index === -1) {
                    selectionDialog.selectedItems.push(item.id);
                  } else if (!isInitialSelected) {
                    // 只有非初始選擇的項目才能取消選擇
                    selectionDialog.selectedItems.splice(index, 1);
                  }
                }
              "
            >
              <q-item-section avatar>
                <q-checkbox
                  v-model="selectionDialog.selectedItems"
                  :val="item.id"
                  color="primary"
                  :disable="
                    selectionDialog.initialSelectedItems.includes(item.id)
                  "
                />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ item.name }}</q-item-label>
                <q-item-label caption>{{ item.id }}</q-item-label>
              </q-item-section>
              <q-item-section
                side
                v-if="selectionDialog.initialSelectedItems.includes(item.id)"
              >
                <q-badge color="primary" label="已選" />
              </q-item-section>
            </q-item>

            <q-inner-loading :showing="selectionDialog.loading">
              <q-spinner-dots size="50px" color="primary" />
            </q-inner-loading>
          </q-list>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            unelevated
            label="確認選擇"
            color="primary"
            @click="confirmSelection"
            :disable="selectionDialog.selectedItems.length === 0"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { useQuasar } from "quasar";
import TiptapEditor from "../components/TiptapEditor.vue";
import { Search } from "lucide-vue-next";

const props = defineProps({
  Permissions: {
    type: [String, Array],
    default: () => [],
  },
  Userid: String,
  Did: String,
  Dname: String,
});

const $q = useQuasar();
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 資料和狀態
const bulletins = ref([]);
const loading = reactive({
  table: false,
  save: false,
  search: false,
  upload: false,
  stats: false,
});
const isAdmin = ref(false);
const userDep = ref("");

// 篩選功能
const activeFilter = ref(null);

// 檢查公告是否已過期
const isExpired = (bulletin) => {
  if (!bulletin.expires_at) return false;
  const expiryDate = new Date(bulletin.expires_at);
  const today = new Date();
  return expiryDate < today;
};

// 計算篩選後的公告列表
const filteredBulletins = computed(() => {
  if (!activeFilter.value) {
    return bulletins.value;
  }

  switch (activeFilter.value) {
    case "active":
      return bulletins.value.filter((b) => b.active === 1 && !isExpired(b));
    case "inactive":
      return bulletins.value.filter((b) => b.active === 0);
    case "expired":
      return bulletins.value.filter((b) => isExpired(b));
    default:
      return bulletins.value;
  }
});

// 分頁相關
const currentPage = ref(1);
const pageSize = ref(5); // 每頁顯示5筆公告

// 計算總頁數
const totalPages = computed(() => {
  return Math.ceil(filteredBulletins.value.length / pageSize.value);
});

// 計算當前頁的公告
const paginatedBulletins = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  return filteredBulletins.value.slice(startIndex, endIndex);
});

// 監聽篩選條件變化，重置頁碼
watch(activeFilter, () => {
  currentPage.value = 1;
});

// 監聽搜尋結果變化，重置頁碼
watch(
  () => bulletins.value.length,
  () => {
    currentPage.value = 1;
  }
);

// 計算各類公告數量
const activeCount = computed(() => {
  return bulletins.value.filter((b) => b.active === 1 && !isExpired(b)).length;
});

const inactiveCount = computed(() => {
  return bulletins.value.filter((b) => b.active === 0).length;
});

const expiredCount = computed(() => {
  return bulletins.value.filter((b) => isExpired(b)).length;
});

// 篩選公告
const filterBulletins = (filter) => {
  activeFilter.value = filter === activeFilter.value ? null : filter;
};

// 選項和常數
const priorityOptions = [
  { label: "最高", value: 1 },
  { label: "高", value: 2 },
  { label: "中", value: 3 },
  { label: "低", value: 4 },
  { label: "最低", value: 5 },
];

const requiredOptions = [
  { label: "必讀", value: 1 },
  { label: "非必讀", value: 0 },
];

const activeOptions = [
  { label: "啟用", value: 1 },
  { label: "停用", value: 0 },
];

const targetTypeOptions = [
  { label: "人員", value: "user" },
  { label: "群組", value: "group" },
  { label: "部門", value: "department" },
  { label: "門市", value: "branch" },
];

const permissionTypeOptions = [
  { label: "查看", value: "view" },
  { label: "接收", value: "receive" },
];

// 表格列定義
const columns = [
  {
    name: "title",
    align: "left",
    label: "標題",
    field: "title",
    sortable: true,
  },
  {
    name: "dep",
    align: "left",
    label: "發布單位",
    field: "dep",
    sortable: true,
  },
  {
    name: "priority",
    align: "center",
    label: "優先等級",
    field: "priority",
    sortable: true,
    format: (val) => priorityOptions.find((o) => o.value === val)?.label || val,
  },
  {
    name: "required",
    align: "center",
    label: "必讀",
    field: "required",
    sortable: true,
  },
  {
    name: "publish_at",
    align: "center",
    label: "發布時間",
    field: "publish_at",
    sortable: true,
    format: (val) => new Date(val).toLocaleString(),
  },
  { name: "active", align: "center", label: "狀態", field: "active" },
  { name: "actions", align: "center", label: "操作" },
];

const statsColumns = [
  { name: "name", align: "left", label: "姓名", field: "name" },
  {
    name: "read_status",
    align: "center",
    label: "閱讀狀態",
    field: "read_status",
  },
  {
    name: "read_at",
    align: "center",
    label: "閱讀時間",
    field: "read_at",
    format: (val) => (val ? new Date(val).toLocaleString() : "-"),
  },
];

// 部門選項
const depOptions = ref([]);

// 新增/編輯公告對話框
const bulletinDialog = reactive({
  show: false,
  isEdit: false,
  data: null,
});

// 搜尋對話框
const searchDialog = reactive({
  show: false,
});

// 附件預覽對話框
const previewDialog = reactive({
  show: false,
  url: "",
  loading: false,
  error: null,
  attachment: null,
});

const bulletinForm = reactive({
  id: null,
  title: "",
  dep: "",
  priority: 1,
  required: 1,
  notify: 0,
  publish_at: null,
  expires_at: null,
  active: 1,
  contents: "",
  targets: [],
  attachments: [],
  // 行事曆相關欄位
  add_to_calendar: false,
  calendar_all_day: true,
  calendar_start_time: "09:00",
  calendar_end_time: "17:00",
  event_id: null, // 關聯的行事曆 ID
});

const searchForm = reactive({
  title: "",
  contents: "",
  dep: "",
  priority: null,
  required: null,
  startDate: "",
  endDate: "",
});

const fileToUpload = ref(null);

// 權限和角色
const canManageBulletin = (bulletin) => {
  // 檢查是否為管理員
  if (Array.isArray(props.Permissions)) {
    // 如果是數組，檢查是否包含 Admin
    if (props.Permissions.some((p) => p.trim().toLowerCase() === "admin")) {
      return true;
    }
  } else if (typeof props.Permissions === "string") {
    // 向後兼容：如果是字符串，直接比較
    if (props.Permissions.trim().toLowerCase() === "admin") {
      return true;
    }
  }

  // 檢查是否為部門管理員
  if (userDep.value && bulletin.dep === userDep.value) {
    return true;
  }

  // 檢查是否為發送人
  if (
    bulletin.created_by &&
    props.Userid &&
    bulletin.created_by.trim() === props.Userid.trim()
  ) {
    return true;
  }

  // 檢查是否有權限
  const bulletinPermissions = bulletin.permissions || [];
  return bulletinPermissions.some(
    (perm) =>
      perm.permission_type === "manage" && perm.target_id === bulletin.id
  );
};

// 方法
const loadBulletins = async () => {
  try {
    loading.table = true;

    // 檢查是否為Admin
    let isAdmin = false;
    if (Array.isArray(props.Permissions)) {
      isAdmin = props.Permissions.some(
        (p) => p.trim().toLowerCase() === "admin"
      );
    } else if (typeof props.Permissions === "string") {
      isAdmin = props.Permissions.trim().toLowerCase() === "admin";
    }

    const response = await apiClient.get(`/btin/get_Bulletins`, {
      params: {
        userId: props.Userid,
        isAdmin: isAdmin,
      },
    });

    if (response.data.success) {
      bulletins.value = response.data.data.map((bulletin) => ({
        ...bulletin,
        dep: bulletin.dep.trim(),
        dep_name: bulletin.dep_name.trim(),
      }));
    } else {
      $q.notify({
        type: "negative",
        message: response.data.message || "獲取公告失敗",
      });
    }
  } catch (error) {
    console.error("獲取公告列表錯誤:", error);
    console.error(
      "詳細錯誤信息:",
      error.response ? error.response.data : error.message
    );
    $q.notify({
      type: "negative",
      message: "獲取公告列表失敗",
    });
  } finally {
    loading.table = false;
  }
};

// 打開搜尋對話框
const openSearchDialog = () => {
  searchDialog.show = true;
};

const clearFilter = () => {
  // 重置所有搜尋條件
  searchForm.title = "";
  searchForm.dep = "";
  searchForm.startDate = "";
  searchForm.priority = null;
  searchForm.required = null;

  // 重置篩選狀態
  activeFilter.value = null;

  // 重新載入公告列表
  loadBulletins();
};

const searchBulletins = async () => {
  try {
    loading.search = true;
    loading.table = true;

    const params = {};

    // 處理標題篩選條件
    if (searchForm.title && searchForm.title.trim() !== "") {
      params.title = searchForm.title.trim();
    }

    // 處理部門篩選條件
    if (searchForm.dep) {
      params.dep = searchForm.dep;
    }

    // 處理發布時間篩選條件
    if (searchForm.startDate && searchForm.startDate.trim() !== "") {
      params.startDate = searchForm.startDate.trim();
    }

    // 添加用戶ID和權限信息
    // 檢查是否為Admin
    let isAdmin = false;
    if (Array.isArray(props.Permissions)) {
      isAdmin = props.Permissions.some(
        (p) => p.trim().toLowerCase() === "admin"
      );
    } else if (typeof props.Permissions === "string") {
      isAdmin = props.Permissions.trim().toLowerCase() === "admin";
    }

    params.userId = props.Userid;
    params.isAdmin = isAdmin;

    console.log("搜尋參數:", params);

    const response = await apiClient.get(`/btin/bulletins/search`, {
      params,
    });

    if (response.data.success) {
      let data = response.data.data;
      bulletins.value = data;

      // 關閉搜尋對話框
      searchDialog.show = false;
    } else {
      $q.notify({
        type: "negative",
        message: response.data.message || "搜尋公告失敗",
      });
    }
  } catch (error) {
    console.error("搜尋公告錯誤:", error);
    $q.notify({
      type: "negative",
      message: "搜尋公告失敗",
    });
  } finally {
    loading.search = false;
    loading.table = false;
  }
};

// 檢查用戶權限
const checkUserRole = async () => {
  try {
    // 設置管理員狀態
    if (Array.isArray(props.Permissions)) {
      // 如果是數組，檢查是否包含 Admin
      isAdmin.value = props.Permissions.some(
        (p) => p.trim().toLowerCase() === "admin"
      );
    } else if (typeof props.Permissions === "string") {
      // 向後兼容：如果是字符串，直接比較
      isAdmin.value = props.Permissions.trim().toLowerCase() === "admin";
    } else {
      isAdmin.value = false;
    }

    // 直接使用從首頁帶入的部門ID
    userDep.value = props.Did || "";
  } catch (error) {
    console.error("檢查用戶權限錯誤:", error);
  }
};

// 目標選項資料
const userOptions = ref([]);
const groupOptions = ref([]);
const departmentOptions = ref([]);
const branchOptions = ref([]);

// 搜尋表單已在上方定義

// 公告表單已在上方定義

const resetBulletinForm = () => ({
  id: null,
  title: "",
  contents: "",
  dep: "",
  priority: 3,
  required: 0,
  notify: 1, // E-mail通知預設開
  publish_at: new Date().toISOString().split("T")[0], // 發布日期預設今天
  expires_at: "",
  active: 1,
  targets: [],
  attachments: [],
  // 行事曆相關欄位
  add_to_calendar: false,
  calendar_all_day: true,
  calendar_start_time: "09:00",
  calendar_end_time: "17:00",
  event_id: null,
});

// 統計對話框
const statsDialog = reactive({
  show: false,
  data: null,
});

// 目標相關
const loadTargetOptions = async () => {
  try {
    // 載入部門選項
    const depsResponse = await apiClient.get(
      `${apiBaseUrl}/deps/get_departments`
    );
    if (depsResponse.data && Array.isArray(depsResponse.data)) {
      // 設置部門選項 (depOptions)
      depOptions.value = depsResponse.data.map((dep) => ({
        label: dep.Name.trim(),
        value: dep.Id.trim(), // 使用中文名稱作為值
      }));
      // 設置部門目標選項 (departmentOptions)
      departmentOptions.value = depsResponse.data.map((dep) => ({
        label: dep.Name.trim(),
        value: dep.Id.trim(),
      }));
    } else {
      console.error("部門資料格式不正確:", depsResponse.data);
      depOptions.value = [];
      departmentOptions.value = [];
    }

    // 載入門市選項
    const branchResponse = await apiClient.get(
      `${apiBaseUrl}/branch/get_branch`
    );
    if (branchResponse.data && Array.isArray(branchResponse.data)) {
      branchOptions.value = branchResponse.data.map((branch) => ({
        label: branch.Cod_name,
        value: branch.Cod_cust,
      }));
    } else {
      console.error("門市資料格式不正確:", branchResponse.data);
      branchOptions.value = [];
    }

    // 載入群組選項
    const groupResponse = await apiClient.get(
      `${apiBaseUrl}/users/get_usersgroup`
    );
    if (groupResponse.data && Array.isArray(groupResponse.data)) {
      groupOptions.value = groupResponse.data.map((group) => ({
        label: group.Name.trim(),
        value: group.Code.trim(),
      }));
    } else {
      console.error("群組資料格式不正確:", groupResponse.data);
      groupOptions.value = [];
    }
  } catch (error) {
    console.error("載入目標選項錯誤:", error);
    // 設置默認空陣列以避免進一步的錯誤
    userOptions.value = [];
    departmentOptions.value = [];
    branchOptions.value = [];
  }
};

const addTarget = () => {
  bulletinForm.targets.push({
    target_type: "user",
    target_id: "",
    permission_type: "view",
    displayText: "點擊選擇對象",
    isExisting: false,
  });
};

const removeTarget = (index) => {
  bulletinForm.targets.splice(index, 1);
};

// 添加 getTargetDisplayText 函數，用於獲取目標對象的顯示文字
const getTargetDisplayText = (target) => {
  if (!target.target_id) return "點擊選擇對象";

  // 由於後端已將 target_id 分開存儲，前端仍然使用逗號分隔的形式
  const ids = target.target_id.split(",").map((id) => id.trim());
  let displayTexts = [];

  try {
    switch (target.target_type.trim()) {
      case "user":
        // 從用戶選項中查找對應的用戶名稱
        for (const id of ids) {
          const user = userOptions.value.find(
            (item) => item.value === id.trim()
          );
          if (user) {
            displayTexts.push(user.label);
          } else {
            displayTexts.push(id); // 如果找不到對應的用戶，顯示ID
          }
        }
        break;
      case "group":
        // 從群組選項中查找對應的群組名稱
        for (const id of ids) {
          const group = groupOptions.value.find(
            (item) => item.value === id.trim()
          );
          if (group) {
            displayTexts.push(group.label);
          } else {
            displayTexts.push(id);
          }
        }
        break;
      case "department":
        // 從部門選項中查找對應的部門名稱
        for (const id of ids) {
          const dept = departmentOptions.value.find(
            (item) => item.value === id.trim()
          );
          if (dept) {
            displayTexts.push(dept.label);
          } else {
            displayTexts.push(id);
          }
        }
        break;
      case "branch":
        // 從門市選項中查找對應的門市名稱
        for (const id of ids) {
          const branch = branchOptions.value.find(
            (item) => item.value === id.trim()
          );
          if (branch) {
            displayTexts.push(branch.label);
          } else {
            displayTexts.push(id);
          }
        }
        break;
      default:
        console.log("未識別的目標類型:", target.target_type);
        displayTexts = ids;
    }
  } catch (error) {
    console.error("顯示標籤時發生錯誤:", error);
    return target.target_id; // 發生錯誤時顯示原始ID
  }

  // 如果沒有找到任何標籤，則顯示原始ID
  const result =
    displayTexts.length > 0 ? displayTexts.join(", ") : target.target_id;

  // 更新 displayText
  if (target.displayText !== result) {
    target.displayText = result;
  }

  return result;
};

// 新增/編輯公告
const openBulletinDialog = async (bulletin = null) => {
  // 重置表單
  Object.assign(bulletinForm, resetBulletinForm());

  // 設置默認發布部門為用戶所屬部門
  if (!bulletin) {
    bulletinForm.dep = props.Did ? props.Did.trim() : props.Did;
  }

  if (bulletin) {
    // 編輯模式
    bulletinDialog.isEdit = true;

    try {
      const response = await apiClient.get(`/btin/bulletins/${bulletin.id}`);
      if (response.data.success) {
        const raw = response.data.data;
        bulletinForm.id = raw.id;
        bulletinForm.title = raw.title;
        bulletinForm.contents = raw.contents;
        bulletinForm.dep = raw.dep ? raw.dep.trim() : raw.dep;
        bulletinForm.priority = raw.priority;
        bulletinForm.required = raw.required;
        bulletinForm.notify = raw.notify;
        bulletinForm.publish_at = raw.publish_at
          ? new Date(raw.publish_at).toISOString().split("T")[0]
          : new Date().toISOString().split("T")[0];
        bulletinForm.expires_at = raw.expires_at
          ? new Date(raw.expires_at).toISOString().split("T")[0]
          : null;
        bulletinForm.active = raw.active;

        // 處理目標對象
        // 由於後端已將每個 target_id 分開存儲，需要按 target_type 分組
        const targetGroups = {};

        if (raw.targets && raw.targets.length > 0) {
          raw.targets.forEach((target) => {
            // 確保 target_type 和 permission_type 沒有空格
            const targetType = target.target_type.trim();
            const permissionType = target.permission_type.trim();
            const targetId = target.target_id.trim();

            const key = `${targetType}_${permissionType}`;
            if (!targetGroups[key]) {
              targetGroups[key] = {
                target_type: targetType,
                permission_type: permissionType,
                target_id: targetId,
                displayText: "",
                isExisting: true, // 標記為已存在的目標對象
              };
            } else {
              targetGroups[key].target_id += `,${targetId}`;
            }
          });

          bulletinForm.targets = Object.values(targetGroups);

          // 更新顯示文本
          bulletinForm.targets.forEach((target) => {
            target.displayText = getTargetDisplayText(target);
            target.isExisting = true; // 確保所有目標對象都被標記為已存在
          });
        }

        // 處理行事曆相關數據
        bulletinForm.add_to_calendar = !!raw.event_id;
        bulletinForm.event_id = raw.event_id || null;

        // 如果有關聯的行事曆，載入行事曆設定
        if (raw.event_id) {
          try {
            const eventResponse = await apiClient.get(
              `/events/${raw.event_id}`
            );
            if (eventResponse.data.success) {
              const eventData = eventResponse.data.data;
              bulletinForm.calendar_all_day = eventData.all_day;

              if (!eventData.all_day) {
                // 解析時間
                const startTime = new Date(eventData.start_datetime);
                const endTime = new Date(eventData.end_datetime);
                bulletinForm.calendar_start_time = startTime.toLocaleTimeString(
                  "zh-TW",
                  {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: false,
                  }
                );
                bulletinForm.calendar_end_time = endTime.toLocaleTimeString(
                  "zh-TW",
                  {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: false,
                  }
                );
              }
            }
          } catch (eventError) {
            console.error("載入行事曆數據失敗:", eventError);
            // 如果載入行事曆失敗，重置行事曆設定
            bulletinForm.add_to_calendar = false;
            bulletinForm.event_id = null;
          }
        }
      }
    } catch (error) {
      console.error("獲取公告詳情失敗:", error);
      $q.notify({
        type: "negative",
        message: "無法獲取公告詳情",
      });
    }
  } else {
    // 新增模式
    bulletinDialog.isEdit = false;
  }

  bulletinDialog.show = true;
};

const saveBulletin = async () => {
  try {
    loading.save = true;

    // 驗證必填欄位
    if (!bulletinForm.title || !bulletinForm.contents || !bulletinForm.dep) {
      $q.notify({
        type: "warning",
        message: "請填寫所有必填欄位",
      });
      return;
    }

    // 驗證行事曆相關欄位
    if (bulletinForm.add_to_calendar) {
      if (!bulletinForm.publish_at || !bulletinForm.expires_at) {
        $q.notify({
          type: "warning",
          message: "加入行事曆時，發布時間和到期時間為必填項目",
        });
        return;
      }

      if (
        new Date(bulletinForm.expires_at) <= new Date(bulletinForm.publish_at)
      ) {
        $q.notify({
          type: "warning",
          message: "到期時間必須大於發布時間",
        });
        return;
      }

      if (!bulletinForm.calendar_all_day) {
        if (
          !bulletinForm.calendar_start_time ||
          !bulletinForm.calendar_end_time
        ) {
          $q.notify({
            type: "warning",
            message: "請設定開始和結束時間",
          });
          return;
        }
      }
    }

    const data = {
      title: bulletinForm.title,
      contents: bulletinForm.contents,
      dep: bulletinForm.dep,
      priority: bulletinForm.priority,
      required: bulletinForm.required,
      notify: bulletinForm.notify,
      publish_at: bulletinForm.publish_at,
      expires_at: bulletinForm.expires_at || null,
      active: bulletinForm.active,
      targets: bulletinForm.targets,
      created_by: props.Userid,
      update_by: props.Userid,
      // 行事曆相關欄位
      add_to_calendar: bulletinForm.add_to_calendar,
      calendar_all_day: bulletinForm.calendar_all_day,
      calendar_start_time: bulletinForm.calendar_start_time,
      calendar_end_time: bulletinForm.calendar_end_time,
      event_id: bulletinForm.event_id,
    };
    //console.log(data);
    let response;

    if (bulletinDialog.isEdit) {
      // 更新
      response = await apiClient.put(
        `/btin/bulletins/${bulletinForm.id}`,
        data
      );
    } else {
      // 新增
      response = await apiClient.post("/btin/bulletins", data);
    }

    if (response.data.success) {
      // 如果設定了發送郵件通知
      if (bulletinForm.notify === 1) {
        try {
          // 處理不同情況：新增公告或更新公告且有新增用戶
          const bulletinId = bulletinDialog.isEdit
            ? bulletinForm.id
            : response.data.data.id;
          const hasNewUsers =
            bulletinDialog.isEdit && response.data.hasNewUsers;

          // 如果是新增公告或者是編輯公告但有新增用戶，則發送郵件通知
          if (!bulletinDialog.isEdit || hasNewUsers) {
            // 等待郵件 API 回應
            await apiClient.post(`/btin/send-notifications`, { bulletinId });
            $q.notify({
              type: "positive",
              message: bulletinDialog.isEdit
                ? "公告更新成功，Mail狀態請至通知紀錄查詢"
                : "公告新增成功，Mail狀態請至通知紀錄查詢",
            });
          } else {
            // 編輯公告但沒有新增用戶，不需要發送郵件
            $q.notify({
              type: "positive",
              message: "公告更新成功",
            });
          }
        } catch (sendError) {
          // 即使發送失敗，也只顯示公告已儲存成功的訊息，並提示查看通知紀錄
          console.error("發送郵件通知失敗:", sendError);
          $q.notify({
            type: "positive",
            message: bulletinDialog.isEdit
              ? "公告更新成功，Mail狀態請至通知紀錄查詢"
              : "公告新增成功，Mail狀態請至通知紀錄查詢",
          });
        }
      } else {
        // 沒有設定發送郵件通知，只顯示公告儲存成功的訊息
        $q.notify({
          type: "positive",
          message: bulletinDialog.isEdit ? "公告更新成功" : "公告新增成功",
        });
      }
      // 儲存與通知都完成後再關閉 dialog 並刷新列表
      bulletinDialog.show = false;
      loadBulletins();
    } else {
      $q.notify({
        type: "negative",
        message:
          response.data.message ||
          (bulletinDialog.isEdit ? "更新失敗" : "新增失敗"),
      });
    }
  } catch (error) {
    console.error("儲存公告錯誤:", error);
    $q.notify({
      type: "negative",
      message: "儲存公告失敗",
    });
  } finally {
    loading.save = false;
  }
};

const confirmDelete = (bulletin) => {
  // 檢查權限：管理員、發布單位相同的用戶或發布者可以刪除
  if (!isAdmin.value) {
    // 檢查是否為發布者
    const isCreator =
      bulletin.created_by &&
      props.Userid &&
      bulletin.created_by.trim() === props.Userid.trim();

    // 檢查是否為相同部門
    const isSameDepartment =
      bulletin.dep &&
      userDep.value &&
      bulletin.dep.trim() === userDep.value.trim();

    // 如果既不是發布者也不是相同部門，則無權限刪除
    if (!isCreator && !isSameDepartment) {
      $q.notify({
        type: "warning",
        message: "您沒有權限刪除此公告",
      });
      return;
    }
  }

  $q.dialog({
    title: "確認刪除",
    message: `確定要刪除公告「${bulletin.title}」嗎？`,
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      const response = await apiClient.delete(`/btin/bulletins/${bulletin.id}`);
      if (response.data.success) {
        $q.notify({
          type: "positive",
          message: "公告刪除成功",
        });
        loadBulletins();
      } else {
        $q.notify({
          type: "negative",
          message: response.data.message || "刪除失敗",
        });
      }
    } catch (error) {
      console.error("刪除公告錯誤:", error);
      $q.notify({
        type: "negative",
        message: "刪除公告失敗",
      });
    }
  });
};

const duplicateBulletin = (bulletin) => {
  // 重置表單
  Object.assign(bulletinForm, resetBulletinForm());

  // 複製公告內容，但不包含id
  const { id, ...rest } = bulletin;

  // 設置表單數據
  Object.keys(bulletinForm).forEach((key) => {
    if (key in rest) {
      bulletinForm[key] = rest[key];
    }
  });

  // 特別處理目標對象和附件
  bulletinForm.targets = bulletin.targets ? [...bulletin.targets] : [];
  bulletinForm.attachments = bulletin.attachments
    ? [...bulletin.attachments]
    : [];

  // 設置為新增模式
  bulletinDialog.isEdit = false;
  bulletinDialog.show = true;
};

const openStatsDialog = async (bulletin) => {
  try {
    statsDialog.data = null;
    statsDialog.show = true;
    loading.stats = true;

    const response = await apiClient.get(
      `/btin/bulletins/${bulletin.id}/stats`
    );
    if (response.data.success) {
      statsDialog.data = cleanStringsDeep(response.data.data);
      //console.log(statsDialog.data);
    } else {
      $q.notify({
        type: "negative",
        message: response.data.message || "獲取統計資料失敗",
      });
      statsDialog.show = false;
    }
  } catch (error) {
    console.error("獲取閱讀統計錯誤:", error);
    $q.notify({
      type: "negative",
      message: "獲取閱讀統計失敗",
    });
    statsDialog.show = false;
  } finally {
    loading.stats = false;
  }
};

// 附件相關
const handleFileSelected = async () => {
  // 檢查文件類型
  if (fileToUpload.value) {
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-powerpoint",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "text/plain",
      "image/jpeg",
      "image/png",
      "image/gif",
    ];

    const files = Array.isArray(fileToUpload.value)
      ? fileToUpload.value
      : [fileToUpload.value];

    // 檢查每個文件的類型
    const invalidFiles = files.filter(
      (file) => !allowedTypes.includes(file.type)
    );

    if (invalidFiles.length > 0) {
      $q.notify({
        type: "negative",
        message: `不支援的檔案類型: ${invalidFiles
          .map((f) => f.name)
          .join(", ")}`,
        caption: "請上傳PDF、Office文件、文字檔或圖片檔案",
      });

      // 從選擇中移除不支援的檔案
      if (Array.isArray(fileToUpload.value)) {
        fileToUpload.value = fileToUpload.value.filter((file) =>
          allowedTypes.includes(file.type)
        );
        if (fileToUpload.value.length === 0) fileToUpload.value = null;
      } else {
        fileToUpload.value = null;
      }
    }
  }
};

const uploadFile = async () => {
  if (!fileToUpload.value || !fileToUpload.value.length || !bulletinForm.id) {
    $q.notify({
      type: "warning",
      message: bulletinForm.id ? "請先選擇文件" : "請先儲存公告後再上傳附件",
    });
    return;
  }

  try {
    loading.upload = true;

    // 處理多個文件上傳
    const files = Array.isArray(fileToUpload.value)
      ? fileToUpload.value
      : [fileToUpload.value];

    // 檢查檔案大小，限制為 5MB
    const maxSize = 5 * 1024 * 1024; // 5MB
    const oversizedFiles = files.filter((file) => file.size > maxSize);

    if (oversizedFiles.length > 0) {
      $q.notify({
        type: "negative",
        message: `檔案過大: ${oversizedFiles.map((f) => f.name).join(", ")}`,
        caption: "檔案大小限制為 5MB",
      });
      return;
    }

    let successCount = 0;
    let failCount = 0;

    for (const file of files) {
      const formData = new FormData();
      formData.append("file", file);

      try {
        // 輸出請求內容以便調試
        console.log("上傳文件:", file.name, "大小:", file.size);

        const response = await apiClient.post(
          `/btin/bulletins/${bulletinForm.id}/attachments`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        if (response.data.success) {
          successCount++;
        } else {
          failCount++;
          console.error("上傳失敗:", response.data);
        }
      } catch (err) {
        console.error(`上傳文件 ${file.name} 失敗:`, err);
        failCount++;
      }
    }

    // 顯示上傳結果
    if (successCount > 0) {
      $q.notify({
        type: "positive",
        message: `成功上傳 ${successCount} 個附件${
          failCount > 0 ? `，${failCount} 個失敗` : ""
        }`,
      });

      // 刷新附件列表
      const bulletinResponse = await apiClient.get(
        `/btin/bulletins/${bulletinForm.id}`
      );
      if (bulletinResponse.data.success) {
        bulletinForm.attachments = bulletinResponse.data.data.attachments || [];
      }

      // 清空選擇的文件
      fileToUpload.value = null;
    } else if (failCount > 0) {
      $q.notify({
        type: "negative",
        message: "所有附件上傳失敗",
      });
    }
  } catch (error) {
    console.error("上傳附件錯誤:", error);
    $q.notify({
      type: "negative",
      message: "上傳附件失敗",
    });
  } finally {
    loading.upload = false;
  }
};

const deleteAttachment = async (attachment) => {
  try {
    console.log("刪除附件:", attachment);
    const response = await apiClient.delete(
      `/btin/bulletins/${attachment.id}/attachments/${attachment.sno}`
    );
    if (response.data.success) {
      $q.notify({
        type: "positive",
        message: "附件刪除成功",
      });

      // 從附件列表中移除
      const index = bulletinForm.attachments.findIndex(
        (a) => a.id === attachment.id && a.sno === attachment.sno
      );
      if (index !== -1) {
        bulletinForm.attachments.splice(index, 1);
      }
    } else {
      $q.notify({
        type: "negative",
        message: response.data.message || "刪除失敗",
      });
    }
  } catch (error) {
    console.error("刪除附件錯誤:", error);
    $q.notify({
      type: "negative",
      message: "刪除附件失敗",
    });
  }
};

// 下載附件
const downloadAttachment = (attachment) => {
  const fileUrl = `${apiBaseUrl}/uploads/${attachment.file_path}`;
  const link = document.createElement("a");
  link.href = fileUrl;
  link.setAttribute(
    "download",
    `附件${attachment.sno}.${attachment.file_type || "pdf"}`
  );
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 預覽附件
const previewAttachment = (attachment) => {
  previewDialog.attachment = attachment;
  previewDialog.loading = true;
  previewDialog.error = null;
  previewDialog.show = true;

  const fileUrl = `${apiBaseUrl}/uploads/${attachment.file_path}`;
  previewDialog.url = fileUrl;

  // 檢查檔案類型，確定是否可以在瀏覽器中預覽
  const fileType = attachment.file_type
    ? attachment.file_type.toLowerCase()
    : "";
  const previewableTypes = ["pdf", "jpg", "jpeg", "png", "gif"];

  if (!previewableTypes.includes(fileType)) {
    previewDialog.error = `無法預覽此類型的檔案 (${
      fileType || "未知"
    })，請下載後查看`;
    previewDialog.loading = false;
    return;
  }

  // 使用 Image 物件檢查圖片是否可以載入
  if (["jpg", "jpeg", "png", "gif"].includes(fileType)) {
    const img = new Image();
    img.onload = () => {
      previewDialog.loading = false;
    };
    img.onerror = () => {
      previewDialog.error = "無法載入圖片，請下載後查看";
      previewDialog.loading = false;
    };
    img.src = fileUrl;
  } else {
    // PDF 或其他類型，直接顯示
    setTimeout(() => {
      previewDialog.loading = false;
    }, 1000);
  }
};

// 處理編輯器中刪除的圖片
const handleImageDeleted = async (imageUrl) => {
  try {
    // 處理上傳到伺服器的圖片
    if (imageUrl.includes("/uploads/")) {
      // 從 URL 中提取相對路徑（btin/YYYYMM/xxx.ext）
      const uploadsIndex = imageUrl.indexOf("/uploads/");
      if (uploadsIndex !== -1) {
        const relativePath = imageUrl.substring(uploadsIndex + 9); // 跳過 "/uploads/"
        if (relativePath) {
          // 調用後端 API 刪除圖片（移除 /api 前綴）
          await apiClient.delete(`/btin/images/${relativePath}`);
          console.log("成功刪除伺服器圖片:", relativePath);
        }
      }
    }
    // Base64圖片不需要額外處理，它們直接嵌入在HTML內容中
    else if (imageUrl.startsWith("data:")) {
      console.log("Base64圖片已從內容中移除");
    }
  } catch (error) {
    console.error("刪除圖片失敗:", error);
    // 不顯示錯誤通知，因為這是背景操作
  }
};

// 優先等級反轉值（用於QRating組件）
const priorityRating = computed({
  get: () => {
    if (bulletinForm.priority === null || bulletinForm.priority === undefined) {
      return 3; // 默認為中等優先級
    }
    return 6 - bulletinForm.priority; // 反轉值：1變5，5變1
  },
  set: (val) => {
    bulletinForm.priority = val === null || val === undefined ? 3 : 6 - val; // 反轉回來：5變1，1變5
  },
});

// 檢查是否可以編輯行事曆
const canEditCalendar = computed(() => {
  // 新增模式下總是可以編輯行事曆
  if (!bulletinForm.id) {
    return true;
  }

  // 編輯模式下，如果公告停用或已過期，則不能編輯行事曆
  return bulletinForm.active === 1 && !isExpired(bulletinForm);
});

// 搜尋用優先等級反轉值
const searchPriorityRating = computed({
  get: () => {
    if (searchForm.priority === null || searchForm.priority === undefined) {
      return null;
    }
    return 6 - searchForm.priority;
  },
  set: (val) => {
    searchForm.priority = val === null || val === undefined ? null : 6 - val;
  },
});

// 部門名稱轉換函數
const getDepartmentName = (depCode) => {
  // 在部門選項中查找匹配的部門

  const dep = depOptions.value.find((dep) => dep.value === depCode.trim());

  // 如果找到匹配的部門，返回其名稱，否則返回原始代碼
  return dep ? dep.label : depCode;
};
// 清除字串前後空白
function cleanStringsDeep(obj) {
  if (Array.isArray(obj)) {
    return obj.map(cleanStringsDeep);
  } else if (obj && typeof obj === "object") {
    const cleaned = {};
    for (const key in obj) {
      const val = obj[key];
      cleaned[key] =
        typeof val === "string" ? val.trim() : cleanStringsDeep(val); // 遞迴處理巢狀物件
    }
    return cleaned;
  } else {
    return obj;
  }
}

// 在 script 區域添加用戶選擇對話框相關變數和函數

// 在 data 部分添加
const selectionDialog = reactive({
  show: false,
  loading: false,
  items: [],
  selectedItems: [],
  searchQuery: "",
  currentTarget: null,
  targetType: "",
  title: "",
  initialSelectedItems: [],
});

// 在 methods 部分添加
const openSelectionDialog = async (target) => {
  try {
    selectionDialog.show = true;
    selectionDialog.loading = true;
    selectionDialog.searchQuery = "";
    selectionDialog.selectedItems = target.target_id
      ? target.target_id.split(",").map((id) => id.trim())
      : [];
    // 保存初始選擇的項目，這些項目將是唯讀的
    selectionDialog.initialSelectedItems = [...selectionDialog.selectedItems];
    selectionDialog.currentTarget = target;
    selectionDialog.targetType = target.target_type.trim();

    // 根據目標類型設置標題和獲取相應的數據
    let response;
    switch (selectionDialog.targetType) {
      case "user":
        selectionDialog.title = "選擇人員";
        break;
      case "group":
        selectionDialog.title = "選擇群組";
        break;
      case "department":
        selectionDialog.title = "選擇部門";
        break;
      case "branch":
        selectionDialog.title = "選擇門市";
        break;
      default:
        console.log("未識別的目標類型:", selectionDialog.targetType);
        selectionDialog.title = "選擇對象";
    }

    // 清空之前的選項列表
    selectionDialog.items = [];

    // 根據目標類型獲取不同的數據
    switch (selectionDialog.targetType) {
      case "user":
        response = await apiClient.post(`${apiBaseUrl}/users/get_enable_users`);
        if (response.data && Array.isArray(response.data)) {
          selectionDialog.items = response.data.map((item) => ({
            id: item.ID.trim(),
            name: item.Name.trim(),
            label: `${item.Name.trim()} (${item.ID.trim()})`,
            value: item.ID.trim(),
          }));
        }
        break;
      case "group":
        response = await apiClient.get(`${apiBaseUrl}/users/get_usersgroup`);
        if (response.data && Array.isArray(response.data)) {
          selectionDialog.items = response.data.map((item) => ({
            id: item.Code.trim(),
            name: item.Name.trim(),
            label: item.Name.trim(),
            value: item.Code.trim(),
          }));
        }
        break;
      case "department":
        response = await apiClient.get(`${apiBaseUrl}/deps/get_departments`);
        if (response.data && Array.isArray(response.data)) {
          selectionDialog.items = response.data.map((item) => ({
            id: item.Id.trim(),
            name: item.Name.trim(),
            label: item.Name.trim(),
            value: item.Id.trim(),
          }));
        }
        break;
      case "branch":
        response = await apiClient.get(`${apiBaseUrl}/branch/get_branch`);
        if (response.data && Array.isArray(response.data)) {
          selectionDialog.items = response.data.map((item) => ({
            id: item.Cod_cust.trim(),
            name: item.Cod_name.trim(),
            label: item.Cod_name.trim(),
            value: item.Cod_cust.trim(),
          }));
        }
        break;
    }

    // 驗證已選項目是否在新的選項列表中
    if (selectionDialog.selectedItems.length > 0) {
      // 過濾出在新選項列表中存在的項目
      selectionDialog.selectedItems = selectionDialog.selectedItems.filter(
        (id) => selectionDialog.items.some((item) => item.value === id)
      );

      // 如果過濾後沒有有效的選中項，清空選中項
      if (selectionDialog.selectedItems.length === 0) {
        // 清空目標ID
        if (selectionDialog.currentTarget) {
          selectionDialog.currentTarget.target_id = "";
          selectionDialog.currentTarget.displayText = "點擊選擇對象";
        }
      }
    }
  } catch (error) {
    console.error(`載入${selectionDialog.title}列表失敗:`, error);
    $q.notify({
      type: "negative",
      message: `無法載入${selectionDialog.title}列表`,
    });
    selectionDialog.items = [];
    // 清空選中項
    selectionDialog.selectedItems = [];
    // 清空目標ID
    if (selectionDialog.currentTarget) {
      selectionDialog.currentTarget.target_id = "";
      selectionDialog.currentTarget.displayText = "點擊選擇對象";
    }
  } finally {
    selectionDialog.loading = false;
  }
};

// 替換 confirmUserSelection 為更通用的 confirmSelection
const confirmSelection = () => {
  try {
    if (
      selectionDialog.currentTarget &&
      selectionDialog.selectedItems.length > 0
    ) {
      // 將選中項目設置為目標ID，確保沒有空格
      selectionDialog.currentTarget.target_id = selectionDialog.selectedItems
        .map((id) => id.trim())
        .join(",");

      // 更新顯示文本
      const displayText = getTargetDisplayText(selectionDialog.currentTarget);
      selectionDialog.currentTarget.displayText = displayText;

      // 強制更新顯示文本
      const targetIndex = bulletinForm.targets.findIndex(
        (t) => t === selectionDialog.currentTarget
      );

      if (targetIndex !== -1) {
        // 使用 Vue 的響應式更新方式
        bulletinForm.targets[targetIndex] = {
          ...bulletinForm.targets[targetIndex],
          target_id: selectionDialog.currentTarget.target_id,
          displayText: displayText,
        };
      }
    } else if (selectionDialog.selectedItems.length === 0) {
      // 如果沒有選擇任何項目，清空目標ID
      if (selectionDialog.currentTarget) {
        selectionDialog.currentTarget.target_id = "";
        selectionDialog.currentTarget.displayText = "點擊選擇對象";

        // 找到對應的目標索引
        const targetIndex = bulletinForm.targets.findIndex(
          (t) => t === selectionDialog.currentTarget
        );

        if (targetIndex !== -1) {
          // 強制更新目標對象
          bulletinForm.targets[targetIndex] = {
            ...bulletinForm.targets[targetIndex],
            target_id: "",
            displayText: "點擊選擇對象",
          };
        }
      }
    }
    selectionDialog.show = false;
  } catch (error) {
    console.error("確認選擇時發生錯誤:", error);
    $q.notify({
      type: "negative",
      message: "確認選擇時發生錯誤",
      timeout: 2000,
    });
  }
};

// 替換 filteredUsers 為更通用的 filteredItems
const filteredItems = computed(() => {
  if (!selectionDialog.searchQuery) return selectionDialog.items;

  const query = selectionDialog.searchQuery.toLowerCase();
  return selectionDialog.items.filter(
    (item) =>
      item.name.toLowerCase().includes(query) ||
      item.id.toLowerCase().includes(query)
  );
});

// 在 methods 部分添加
const handleTargetTypeChange = async (newType, index) => {
  // 當目標類型變更時，清空目標ID
  if (bulletinForm.targets[index]) {
    // 清空目標ID
    bulletinForm.targets[index].target_id = "";
    bulletinForm.targets[index].displayText = "點擊選擇對象";

    // 強制更新目標對象
    bulletinForm.targets[index] = {
      ...bulletinForm.targets[index],
      target_id: "",
      displayText: "點擊選擇對象",
    };

    // 自動打開選擇對話框
    await openSelectionDialog(bulletinForm.targets[index]);
  }
};

// 生命週期鉤子
onMounted(() => {
  // 檢查用戶權限
  checkUserRole();
  // 載入目標選項（同時處理部門選項）
  loadTargetOptions();
  // 載入公告列表
  loadBulletins();

  // 初始化 displayText
  if (bulletinForm.targets && bulletinForm.targets.length > 0) {
    bulletinForm.targets.forEach((target) => {
      if (!target.displayText) {
        target.displayText = getTargetDisplayText(target);
      }
    });
  }
});

// 在 methods 部分添加 getSelectedCountText 函數
const getSelectedCountText = (target) => {
  if (!target.target_id) return "0";

  const count = target.target_id.split(",").length;
  return `${count} `;
};

// 添加全選功能
const selectAllVisibleItems = () => {
  // 獲取當前過濾後的項目ID列表
  const visibleItemIds = filteredItems.value.map((item) => item.id);

  // 檢查是否所有可見項目都已選中
  const allSelected = visibleItemIds.every((id) =>
    selectionDialog.selectedItems.includes(id)
  );

  if (allSelected) {
    // 如果全部已選中，則取消全選（但保留初始選擇的項目）
    selectionDialog.selectedItems = selectionDialog.selectedItems.filter(
      (id) =>
        selectionDialog.initialSelectedItems.includes(id) ||
        !visibleItemIds.includes(id)
    );
  } else {
    // 如果不是全部選中，則全選
    // 先創建一個新的Set來去重
    const uniqueIds = new Set([
      ...selectionDialog.selectedItems,
      ...visibleItemIds,
    ]);
    selectionDialog.selectedItems = Array.from(uniqueIds);
  }
};

// 統計表格分頁設定
const statsPagination = ref({
  sortBy: "read_status",
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

const createBulletin = async () => {
  try {
    loading.save = true;
    bulletinDialog.error = null;

    // 驗證表單
    if (!bulletinForm.title) {
      bulletinDialog.error = "請輸入公告標題";
      loading.save = false;
      return;
    }

    if (!bulletinForm.contents) {
      bulletinDialog.error = "請輸入公告內容";
      loading.save = false;
      return;
    }

    if (!bulletinForm.dep) {
      bulletinDialog.error = "請選擇發布單位";
      loading.save = false;
      return;
    }

    // 如果沒有選擇目標對象，添加一個錯誤提示
    if (bulletinForm.targets.length === 0) {
      bulletinDialog.error = "請選擇至少一個目標對象";
      loading.save = false;
      return;
    }

    // 添加創建者ID
    const formData = {
      ...bulletinForm,
      created_by: props.Userid,
    };

    // 發送請求
    const response = await apiClient.post(`/btin/create_bulletin`, formData);

    if (response.data.success) {
      $q.notify({
        type: "positive",
        message: "公告新增成功",
      });

      // 關閉對話框
      bulletinDialog.show = false;

      // 重新載入公告列表
      loadBulletins();
    } else {
      bulletinDialog.error = response.data.message || "公告新增失敗";
    }
  } catch (error) {
    console.error("新增公告錯誤:", error);
    bulletinDialog.error = "伺服器錯誤，無法新增公告";
  } finally {
    loading.save = false;
  }
};

const updateBulletin = async () => {
  try {
    loading.save = true;
    bulletinDialog.error = null;

    // 驗證表單
    if (!bulletinForm.title) {
      bulletinDialog.error = "請輸入公告標題";
      loading.save = false;
      return;
    }

    if (!bulletinForm.contents) {
      bulletinDialog.error = "請輸入公告內容";
      loading.save = false;
      return;
    }

    if (!bulletinForm.dep) {
      bulletinDialog.error = "請選擇發布單位";
      loading.save = false;
      return;
    }

    // 如果沒有選擇目標對象，添加一個錯誤提示
    if (bulletinForm.targets.length === 0) {
      bulletinDialog.error = "請選擇至少一個目標對象";
      loading.save = false;
      return;
    }

    // 添加更新者ID
    const formData = {
      ...bulletinForm,
      update_by: props.Userid,
    };

    // 發送請求
    const response = await apiClient.put(
      `/btin/update_bulletin/${bulletinForm.id}`,
      formData
    );

    if (response.data.success) {
      $q.notify({
        type: "positive",
        message: "公告更新成功",
      });

      // 關閉對話框
      bulletinDialog.show = false;

      // 重新載入公告列表
      loadBulletins();
    } else {
      bulletinDialog.error = response.data.message || "公告更新失敗";
    }
  } catch (error) {
    console.error("更新公告錯誤:", error);
    bulletinDialog.error = "伺服器錯誤，無法更新公告";
  } finally {
    loading.save = false;
  }
};
</script>

<style scoped>
.menu-header {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: white;
}

.menu-list-scroll {
  overflow-y: auto;
  flex: 1;
}

.content-container {
  overflow-wrap: break-word;
  word-wrap: break-word;
  max-height: 500px;
  overflow-y: auto;
}

.content-container::-webkit-scrollbar {
  width: 6px;
}

.content-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-container::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.content-container::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

/* 保留內容中的文本對齊方式 */
.content-container :deep(p) {
  text-align: inherit;
  margin: 0.5em 0;
}

.content-container :deep(img) {
  max-width: 100%;
  height: auto;
  /* 移除 display: block 屬性，保留圖片的對齊方式 */
  margin: 0 auto; /* 當段落設置為居中時，這會輔助居中效果 */
}

/* 確保段落內的樣式能正確顯示 */
.content-container :deep([style*="text-align: center"]) {
  text-align: center !important;
}

.content-container :deep([style*="text-align: right"]) {
  text-align: right !important;
}

.content-container :deep([style*="text-align: left"]) {
  text-align: left !important;
}

.content-container :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 10px;
}

.content-container :deep(th),
.content-container :deep(td) {
  border: 1px solid #ddd;
  padding: 8px;
}

.content-container :deep(pre) {
  white-space: pre-wrap;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

/* 標題區塊樣式 */
.title-section {
  padding-left: 16px;
  padding-right: 16px;
}

/* 搜尋按鈕樣式 */
.search-button {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 統計卡片樣式 */
.statistics-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
  overflow: hidden;
}

/* 進度圓環容器 */
.progress-container {
  position: relative;
  padding: 10px;
}

/* 進度圓環內容 */
.progress-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

/* 統計列表樣式 */
.stats-list {
  background: transparent;
  padding: 0 8px;
}

/* 統計項目樣式 */
.stats-item {
  padding: 12px 8px;
}

/* 統計標籤 */
.stats-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

/* 統計數值 */
.stats-value {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.2;
}

/* 統計數值單位 */
.stats-value .text-caption {
  font-size: 14px;
  font-weight: normal;
  opacity: 0.7;
}

/* 分隔線樣式 */
.q-separator.spaced.inset {
  margin-left: 56px;
}

/* 公告列表樣式 */
.bulletin-list {
  margin: 0;
  padding: 0;
}

.bulletin-header {
  min-height: auto;
  padding: 0;
}

.bulletin-badge {
  font-size: 10px;
  padding: 0 4px;
  height: 18px;
}

.bulletin-title {
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
  padding: 0 2px;
  font-weight: 500;
  color: #1976d2;
}

.bulletin-meta {
  font-size: 11px;
}

.attachment-link {
  font-size: 12px;
}

/* 載入動畫樣式 */
.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: loader-rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #1976d2;
  stroke-width: 5;
  stroke-dasharray: 150, 200;
  stroke-dashoffset: -10;
  stroke-linecap: round;
  animation: loader-dash 1.5s ease-in-out infinite;
}

.loader-message {
  margin-top: 12px;
  color: #666;
  font-weight: 500;
  font-size: 14px;
}

@keyframes loader-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loader-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -125;
  }
}

/* 空狀態樣式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 300px;
  text-align: center;
}

.empty-icon {
  width: 60px;
  height: 60px;
  fill: #e0e0e0;
  margin-bottom: 12px;
}

.empty-text {
  color: #757575;
  font-size: 14px;
  font-weight: 500;
}

/* 載入動畫樣式 */
.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

/* 分頁控制樣式 */
.pagination-controls {
  display: flex;
  justify-content: center;
}

@media (max-width: 599px) {
  .pagination-controls {
    transform: scale(0.85);
  }
}

/* 標題和統計卡片容器樣式 */
.title-stats-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 8px;
}

/* 為標題和統計卡片容器中的卡片添加陰影和背景 */
.title-stats-container .q-card {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loader-container .q-spinner {
  margin-bottom: 10px;
}

.loader-container .text-subtitle1 {
  color: #777;
  font-weight: 500;
}

/* 空狀態樣式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 300px;
  text-align: center;
}

.empty-icon {
  width: 60px;
  height: 60px;
  fill: #e0e0e0;
  margin-bottom: 12px;
}

.empty-text {
  color: #757575;
  font-size: 14px;
  font-weight: 500;
}

/* 載入動畫樣式 */
.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

/* 分頁控制樣式 */
.pagination-controls {
  display: flex;
  justify-content: center;
}

@media (max-width: 599px) {
  .pagination-controls {
    transform: scale(0.85);
  }
}

/* 標題和統計卡片容器樣式 */
.title-stats-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 8px;
}

/* 為標題和統計卡片容器中的卡片添加陰影和背景 */
.title-stats-container .q-card {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

/* 統計卡片樣式 */
.stats-container {
  padding: 8px 0;
}

.stats-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-bottom: 6px;
  background-color: #fff;
  border: 1px solid #eaeaea;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.stats-svg {
  width: 24px;
  height: 24px;
  fill: currentColor;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
}

.stats-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 卡片特定樣式 */
.all-card::before {
  background-color: #4caf50;
}

.all-card .stats-icon {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.active-card::before {
  background-color: #1976d2;
}

.active-card .stats-icon {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}

.inactive-card::before {
  background-color: #ff9800;
}

.inactive-card .stats-icon {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.expired-card::before {
  background-color: #9e9e9e;
}

.expired-card .stats-icon {
  color: #9e9e9e;
  background-color: rgba(158, 158, 158, 0.1);
}

/* 活動篩選樣式 */
.active-filter {
  border-color: currentColor;
  background-color: rgba(0, 0, 0, 0.02);
}

.active-filter::before {
  width: 6px;
}

.active-filter .stats-value {
  color: currentColor;
}

/* 響應式調整 */
@media (max-width: 599px) {
  .title-section {
    padding-left: 12px;
    padding-right: 12px;
  }

  .stats-card {
    padding: 8px;
  }

  .stats-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .stats-svg {
    width: 20px;
    height: 20px;
  }

  .stats-value {
    font-size: 16px;
    margin-bottom: 1px;
  }

  .stats-label {
    font-size: 10px;
  }

  .bulletin-title {
    font-size: 13px;
  }

  .bulletin-meta {
    font-size: 10px;
  }
}

/* 超小螢幕響應式調整 */
@media (max-width: 340px) {
  .stats-icon {
    width: 28px;
    height: 28px;
    margin-right: 6px;
  }

  .stats-svg {
    width: 16px;
    height: 16px;
  }

  .stats-value {
    font-size: 14px;
  }

  .stats-label {
    font-size: 9px;
  }

  .bulletin-title {
    font-size: 12px;
  }

  .bulletin-meta {
    font-size: 9px;
  }
}

/* 行事曆設定樣式 - 已移除外框樣式，保持簡潔 */

.event-input {
  background: white;
}

.event-input .q-field__control {
  background: white;
}
</style>
