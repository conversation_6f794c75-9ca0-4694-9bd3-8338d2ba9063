<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 頁面標題卡片 -->
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">行事曆設定</div>
          <div>
            <q-btn
              flat
              round
              size="sm"
              color="primary"
              :class="{
                'text-primary': activeTab === 'departments',
                'text-grey-6': activeTab !== 'departments',
              }"
              @click="activeTab = 'departments'"
            >
              <palette :stroke-width="1" :size="26" />
              <q-tooltip>部門顏色設定</q-tooltip>
            </q-btn>
            <q-btn
              flat
              round
              size="sm"
              color="primary"
              :class="{
                'text-primary': activeTab === 'access',
                'text-grey-6': activeTab !== 'access',
              }"
              @click="activeTab = 'access'"
              class="q-ml-md"
            >
              <lock :stroke-width="1" :size="26" />
              <q-tooltip>行事曆授權設定</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />
    </q-card>

    <!-- 主內容區域 -->
    <q-card class="q-mt-sm no-shadow" style="width: 100%; border-radius: 10px">
      <q-card-section class="q-pa-md">
        <!-- 部門顏色設定 -->
        <q-tab-panels v-model="activeTab" animated>
          <q-tab-panel name="departments" class="q-pa-none">
            <div class="text-primary q-mb-md flex items-center">
              <palette :stroke-width="1" :size="20" class="q-mr-sm" />
              部門顏色設定
            </div>

            <!-- 部門顏色列表 -->
            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-table
                  :rows="departments"
                  :columns="departmentColumns"
                  row-key="id"
                  :pagination="{ rowsPerPage: 10 }"
                  :loading="loading"
                  flat
                  bordered
                >
                  <template v-slot:body="props">
                    <q-tr :props="props">
                      <q-td key="name" :props="props">
                        {{ props.row.name }}
                      </q-td>
                      <q-td key="color_preview" :props="props">
                        <div class="row items-center justify-center">
                          <div
                            class="color-preview cursor-pointer"
                            :style="{
                              backgroundColor: props.row.color || '#e3f2fd',
                            }"
                            @click="openColorPicker(props.row)"
                          ></div>
                        </div>
                      </q-td>
                      <q-td
                        key="actions"
                        :props="props"
                        style="width: 0px; padding: 0"
                      ></q-td>
                    </q-tr>
                  </template>
                </q-table>
              </div>
            </div>
          </q-tab-panel>

          <!-- 行事曆授權設定 -->
          <q-tab-panel name="access" class="q-pa-none">
            <div class="text-primary q-mb-md flex items-center">
              <lock :stroke-width="1" :size="20" class="q-mr-sm" />
              行事曆授權設定
            </div>

            <!-- 群組選擇 -->
            <div class="row q-col-gutter-md q-mb-md">
              <div class="col-12 col-sm-6">
                <q-select
                  v-model="selectedGroup"
                  :options="groupOptions"
                  label="選擇群組"
                  outlined
                  dense
                  emit-value
                  map-options
                  @update:model-value="loadGroupAccess"
                >
                  <template v-slot:prepend>
                    <users :stroke-width="1.5" :size="18" />
                  </template>
                </q-select>
              </div>
            </div>

            <!-- 授權設定表格 -->
            <div class="row q-col-gutter-md" v-if="selectedGroup">
              <div class="col-12">
                <q-table
                  :rows="accessSettings"
                  :columns="accessColumns"
                  row-key="target_type"
                  :pagination="{ rowsPerPage: 10 }"
                  :loading="loading"
                  flat
                  bordered
                >
                  <template v-slot:body="props">
                    <q-tr :props="props">
                      <q-td key="target_type" :props="props">
                        {{ getTargetTypeLabel(props.row.target_type) }}
                      </q-td>
                      <q-td key="can_access" :props="props">
                        <q-toggle
                          v-model="props.row.can_access"
                          @update:model-value="updateAccess(props.row)"
                          dense
                        />
                      </q-td>
                      <q-td key="access_type" :props="props">
                        <q-select
                          v-model="props.row.access_type"
                          :options="accessTypeOptions"
                          outlined
                          dense
                          emit-value
                          map-options
                          :disable="!props.row.can_access"
                          @update:model-value="updateAccess(props.row)"
                        />
                      </q-td>
                      <q-td key="specific_targets" :props="props">
                        <q-btn
                          flat
                          round
                          size="sm"
                          color="primary"
                          :disable="
                            !props.row.can_access ||
                            props.row.access_type !== 'specific'
                          "
                          @click="openTargetSelector(props.row)"
                        >
                          <list :stroke-width="1.5" :size="18" />
                          <q-tooltip>選擇特定對象</q-tooltip>
                        </q-btn>
                      </q-td>
                    </q-tr>
                  </template>
                </q-table>
              </div>
            </div>
          </q-tab-panel>
        </q-tab-panels>
      </q-card-section>
    </q-card>

    <!-- 顏色選擇對話框 -->
    <q-dialog v-model="colorDialog.show" persistent>
      <q-card style="min-width: 350px">
        <q-card-section class="row items-center">
          <div class="text-h6">選擇部門顏色</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <div class="row q-mb-md">
            <div class="col-12">
              <div class="text-subtitle2">
                {{ colorDialog.department?.name }}
              </div>
            </div>
          </div>
          <div class="row q-col-gutter-md">
            <div class="col-12">
              <q-color
                v-model="colorDialog.color"
                default-view="palette"
                no-header
              />
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey-7" v-close-popup />
          <q-btn flat label="確認" color="primary" @click="saveColor" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 目標對象選擇對話框 -->
    <q-dialog v-model="targetDialog.show" persistent>
      <q-card style="min-width: 500px; max-width: 80vw">
        <q-card-section class="row items-center">
          <div class="text-h6">
            選擇{{ getTargetTypeLabel(targetDialog.targetType) }}
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <div class="row q-mb-md">
            <div class="col-12">
              <q-input
                v-model="targetDialog.searchQuery"
                label="搜尋"
                outlined
                dense
                clearable
              >
                <template v-slot:prepend>
                  <search :stroke-width="1.5" :size="18" />
                </template>
              </q-input>
            </div>
          </div>
          <div class="row">
            <div class="col-12" style="max-height: 300px; overflow-y: auto">
              <q-list>
                <q-item
                  v-for="option in filteredTargetOptions"
                  :key="option.value"
                  tag="label"
                  v-ripple
                >
                  <q-item-section avatar>
                    <q-checkbox
                      v-model="targetDialog.selectedItems"
                      :val="option.value"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ option.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey-7" v-close-popup />
          <q-btn flat label="確認" color="primary" @click="saveTargets" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useQuasar } from "quasar";
import apiClient from "../api";

const $q = useQuasar();
const activeTab = ref("departments");
const loading = ref(false);

// 部門顏色設定
const departments = ref([]);
const departmentColumns = [
  {
    name: "name",
    label: "部門名稱",
    field: "name",
    align: "left",
    sortable: true,
  },
  { name: "color_preview", label: "顏色", field: "color", align: "center" },
];

// 顏色選擇對話框
const colorDialog = ref({
  show: false,
  department: null,
  color: "#e3f2fd",
});

// 行事曆授權設定
const selectedGroup = ref(null);
const groupOptions = ref([]);
const accessSettings = ref([]);
const accessColumns = [
  {
    name: "target_type",
    label: "目標類型",
    field: "target_type",
    align: "left",
  },
  {
    name: "can_access",
    label: "允許存取",
    field: "can_access",
    align: "center",
  },
  {
    name: "access_type",
    label: "存取類型",
    field: "access_type",
    align: "left",
  },
  {
    name: "specific_targets",
    label: "特定對象",
    field: "specific_targets",
    align: "center",
  },
];

const accessTypeOptions = [
  { label: "所有對象", value: "all" },
  { label: "僅自己擁有/授權的", value: "own" },
  { label: "特定對象", value: "specific" },
];

// 目標對象選擇對話框
const targetDialog = ref({
  show: false,
  targetType: "",
  searchQuery: "",
  options: [],
  selectedItems: [],
  currentRow: null,
});

// 過濾後的目標選項
const filteredTargetOptions = computed(() => {
  if (!targetDialog.value.searchQuery) {
    return targetDialog.value.options;
  }

  const query = targetDialog.value.searchQuery.toLowerCase();
  return targetDialog.value.options.filter((option) =>
    option.label.toLowerCase().includes(query)
  );
});

// 獲取目標類型標籤
const getTargetTypeLabel = (type) => {
  switch (type) {
    case "user":
      return "人員";
    case "group":
      return "群組";
    case "department":
      return "部門";
    case "branch":
      return "門市";
    default:
      return type;
  }
};

// 載入部門列表
const loadDepartments = async () => {
  try {
    loading.value = true;
    const response = await apiClient.get("/calendarset/department_colors");
    if (response.data.success) {
      departments.value = cleanStringsDeep(response.data.data);
    }
  } catch (error) {
    console.error("載入部門失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入部門資料失敗",
      position: "top",
    });
  } finally {
    loading.value = false;
  }
};

// 打開顏色選擇器
const openColorPicker = (department) => {
  colorDialog.value.department = department;
  colorDialog.value.color = department.color || "#e3f2fd";
  colorDialog.value.show = true;
};

// 保存部門顏色
const saveColor = async () => {
  try {
    loading.value = true;
    const response = await apiClient.post(
      "/calendarset/update_department_color",
      {
        id: colorDialog.value.department.id,
        color: colorDialog.value.color,
      }
    );

    if (response.data.success) {
      // 更新本地數據
      const index = departments.value.findIndex(
        (d) => d.id === colorDialog.value.department.id
      );
      if (index !== -1) {
        departments.value[index].color = colorDialog.value.color;
      }

      $q.notify({
        type: "positive",
        message: "部門顏色更新成功",
        position: "top",
      });

      colorDialog.value.show = false;
    }
  } catch (error) {
    console.error("更新部門顏色失敗:", error);
    $q.notify({
      type: "negative",
      message: "更新部門顏色失敗",
      position: "top",
    });
  } finally {
    loading.value = false;
  }
};

// 載入群組選項
const loadGroups = async () => {
  try {
    loading.value = true;
    const response = await apiClient.get("/users/get_usersgroup");
    if (response.data) {
      // 過濾掉 admin 群組，不在選擇中顯示
      const cleanedData = cleanStringsDeep(response.data);
      groupOptions.value = cleanedData
        .filter((group) => group.Code.toLowerCase() !== "admin")
        .map((group) => ({
          label: group.Name,
          value: group.Code,
        }));
    }
  } catch (error) {
    console.error("載入群組失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入群組資料失敗",
      position: "top",
    });
  } finally {
    loading.value = false;
  }
};

// 載入群組的權限設定
const loadGroupAccess = async () => {
  if (!selectedGroup.value) return;

  try {
    loading.value = true;
    const response = await apiClient.get(
      `/calendarset/group_access/${selectedGroup.value}`
    );

    if (response.data.success) {
      // 初始化四種目標類型的權限設定
      accessSettings.value = [
        {
          target_type: "user",
          can_access: false,
          access_type: "own",
          specific_targets: [],
        },
        {
          target_type: "group",
          can_access: false,
          access_type: "own",
          specific_targets: [],
        },
        {
          target_type: "department",
          can_access: false,
          access_type: "own",
          specific_targets: [],
        },
        {
          target_type: "branch",
          can_access: false,
          access_type: "own",
          specific_targets: [],
        },
      ];

      // 檢查是否有任何權限設定，如果有，則將對應的 can_access 設為 true
      if (response.data.data && response.data.data.length > 0) {
        const targetTypes = [
          ...new Set(response.data.data.map((item) => item.target_type)),
        ];
        targetTypes.forEach((type) => {
          const index = accessSettings.value.findIndex(
            (a) => a.target_type === type
          );
          if (index !== -1) {
            accessSettings.value[index].can_access = true;
          }
        });
      }

      // 更新從服務器獲取的權限設定
      const accessData = cleanStringsDeep(response.data.data);

      // 先將相同類型的權限設定分組
      const groupedAccess = {};
      accessData.forEach((access) => {
        if (!groupedAccess[access.target_type]) {
          groupedAccess[access.target_type] = [];
        }
        groupedAccess[access.target_type].push(access);
      });

      // 處理每種類型的權限設定
      Object.keys(groupedAccess).forEach((targetType) => {
        const index = accessSettings.value.findIndex(
          (a) => a.target_type === targetType
        );

        if (index !== -1) {
          const accessList = groupedAccess[targetType];
          accessSettings.value[index].can_access = true;

          // 檢查是否有 "ALL" 或 OWN 的設定
          const hasAllAccess = accessList.some((a) => a.target_id === "ALL");
          const hasOwnAccess = accessList.some((a) => a.target_id === "OWN");

          if (hasAllAccess) {
            // 如果有 "ALL" 設定，表示可以訪問所有
            accessSettings.value[index].access_type = "all";
            accessSettings.value[index].specific_targets = [];
          } else if (hasOwnAccess) {
            // 如果有 OWN 設定，表示只能訪問自己的
            accessSettings.value[index].access_type = "own";
            accessSettings.value[index].specific_targets = [];
          } else {
            // 否則是特定對象設定
            accessSettings.value[index].access_type = "specific";
            accessSettings.value[index].specific_targets = accessList
              .filter((a) => a.target_id !== "ALL" && a.target_id !== "OWN")
              .map((a) => a.target_id);
          }
        }
      });
    }
  } catch (error) {
    console.error("載入群組權限失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入群組權限資料失敗",
      position: "top",
    });
  } finally {
    loading.value = false;
  }
};

// 更新權限設定
const updateAccess = async (row) => {
  try {
    loading.value = true;

    // 根據 access_type 設定 target_id
    let target_id = "ALL";
    if (row.access_type === "own") {
      target_id = "OWN";
    } else if (row.access_type === "all") {
      target_id = "ALL";
    } else if (
      row.access_type === "specific" &&
      row.specific_targets.length > 0
    ) {
      // 如果是特定對象，但沒有選擇任何對象，則使用 OWN
      // 清理每個 target_id 並移除空白
      const cleanTargets = row.specific_targets.map((t) =>
        typeof t === "string" ? t.trim() : t
      );
      target_id = cleanTargets.join(",");
    } else {
      target_id = "OWN"; // 默認使用 OWN
    }

    // 準備要發送的資料並清理
    const requestData = cleanStringsDeep({
      group_id: selectedGroup.value,
      target_type: row.target_type,
      can_access: row.can_access,
      target_id: row.can_access ? target_id : "ALL", // 如果不允許訪問，則 target_id 為 "ALL"
    });

    const response = await apiClient.post(
      "/calendarset/update_group_access",
      requestData
    );

    if (response.data.success) {
      $q.notify({
        type: "positive",
        message: "權限設定更新成功",
        position: "top",
      });
    }
  } catch (error) {
    console.error("更新權限設定失敗:", error);
    $q.notify({
      type: "negative",
      message: "更新權限設定失敗",
      position: "top",
    });
  } finally {
    loading.value = false;
  }
};

// 打開目標對象選擇器
const openTargetSelector = async (row) => {
  targetDialog.value.targetType = row.target_type;
  targetDialog.value.currentRow = row;
  targetDialog.value.selectedItems = [...row.specific_targets];
  targetDialog.value.searchQuery = "";

  try {
    loading.value = true;

    // 根據目標類型載入選項
    switch (row.target_type) {
      case "user":
        const userResponse = await apiClient.post("/users/get_enable_users");
        const cleanedUsers = cleanStringsDeep(userResponse.data);
        targetDialog.value.options = cleanedUsers.map((user) => ({
          label: `${user.Name} (${user.ID})`,
          value: user.ID,
        }));
        break;

      case "group":
        const groupResponse = await apiClient.get("/users/get_usersgroup");
        const cleanedGroups = cleanStringsDeep(groupResponse.data);
        targetDialog.value.options = cleanedGroups.map((group) => ({
          label: group.Name,
          value: group.Code,
        }));
        break;

      case "department":
        const depResponse = await apiClient.get("/deps/get_deps");
        const cleanedDeps = cleanStringsDeep(depResponse.data);
        targetDialog.value.options = cleanedDeps.map((dep) => ({
          label: dep.Name,
          value: dep.Id,
        }));
        break;

      case "branch":
        const branchResponse = await apiClient.get("/branch/get_branch");
        const cleanedBranches = cleanStringsDeep(branchResponse.data);
        targetDialog.value.options = cleanedBranches
          .filter((branch) => branch.Sts === "1") // 只顯示啟用的門市
          .map((branch) => ({
            label: branch.Cod_name,
            value: branch.Cod_cust,
          }));
        break;
    }

    targetDialog.value.show = true;
  } catch (error) {
    console.error("載入目標對象失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入目標對象失敗",
      position: "top",
    });
  } finally {
    loading.value = false;
  }
};

// 保存選擇的目標對象
const saveTargets = async () => {
  const row = targetDialog.value.currentRow;

  // 更新本地數據
  row.specific_targets = [...targetDialog.value.selectedItems];

  // 調用更新權限的函數
  await updateAccess(row);

  targetDialog.value.show = false;
};

// 清除字串前後空白
function cleanStringsDeep(obj) {
  if (Array.isArray(obj)) {
    return obj.map(cleanStringsDeep);
  } else if (obj && typeof obj === "object") {
    const cleaned = {};
    for (const key in obj) {
      const val = obj[key];
      cleaned[key] =
        typeof val === "string" ? val.trim() : cleanStringsDeep(val); // 遞迴處理巢狀物件
    }
    return cleaned;
  } else {
    return obj;
  }
}

// 初始化
onMounted(async () => {
  await loadDepartments();
  await loadGroups();
});
</script>

<style scoped>
.custom-separator {
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(25, 118, 210, 0.7) 0%,
    rgba(25, 118, 210, 0.2) 100%
  );
  border: none;
}

.color-preview {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid #ddd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.color-preview:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}
</style>
