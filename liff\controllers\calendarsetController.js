const sql = require("mssql");
const dbConfig = require("../config/db");

// 獲取部門顏色設定
exports.getDepartmentColors = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);

    // 查詢部門及其顏色設定
    const result = await pool.request().query(`
      SELECT 
        Id as id, 
        Name as name, 
        color
      FROM Dep
      ORDER BY type
    `);

    return res.json({
      success: true,
      data: result.recordset,
    });
  } catch (error) {
    console.error("獲取部門顏色設定時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "獲取部門顏色設定時發生錯誤",
      error: error.message,
    });
  }
};

// 更新部門顏色
exports.updateDepartmentColor = async (req, res) => {
  const { id, color } = req.body;

  if (!id) {
    return res.status(400).json({
      success: false,
      message: "缺少部門ID",
    });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 檢查部門是否存在
    const checkResult = await pool
      .request()
      .input("id", sql.NVarChar, id)
      .query("SELECT Id FROM Dep WHERE Id = @id");

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: "找不到指定的部門",
      });
    }

    // 更新部門顏色
    await pool
      .request()
      .input("id", sql.NVarChar, id)
      .input("color", sql.NVarChar, color || "#e3f2fd")
      .query("UPDATE Dep SET color = @color WHERE Id = @id");

    return res.json({
      success: true,
      message: "部門顏色更新成功",
    });
  } catch (error) {
    console.error("更新部門顏色時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "更新部門顏色時發生錯誤",
      error: error.message,
    });
  }
};

// 獲取群組的權限設定
exports.getGroupAccess = async (req, res) => {
  const { groupId } = req.params;

  if (!groupId) {
    return res.status(400).json({
      success: false,
      message: "缺少群組ID",
    });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 查詢群組的權限設定
    const result = await pool.request().input("groupId", sql.NVarChar, groupId)
      .query(`
        SELECT 
          group_id, 
          target_type, 
          target_id
        FROM Events_access
        WHERE group_id = @groupId
      `);

    return res.json({
      success: true,
      data: result.recordset,
    });
  } catch (error) {
    console.error("獲取群組權限設定時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "獲取群組權限設定時發生錯誤",
      error: error.message,
    });
  }
};

// 更新群組的權限設定
exports.updateGroupAccess = async (req, res) => {
  const { group_id, target_type, can_access, target_id } = req.body;

  if (!group_id || !target_type) {
    return res.status(400).json({
      success: false,
      message: "缺少必要參數",
    });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 先刪除現有的權限設定
    await pool
      .request()
      .input("groupId", sql.NVarChar, group_id)
      .input("targetType", sql.NVarChar, target_type).query(`
        DELETE FROM Events_access 
        WHERE group_id = @groupId AND target_type = @targetType
      `);

    // 如果允許訪問，則插入新的權限設定
    if (can_access) {
      // 如果是特定對象，可能有多個 target_id（逗號分隔的字符串）
      if (target_id && target_id !== "OWN" && target_id !== "ALL") {
        const targetIds = target_id.split(",");

        for (const id of targetIds) {
          await pool
            .request()
            .input("groupId", sql.NVarChar, group_id)
            .input("targetType", sql.NVarChar, target_type)
            .input("targetId", sql.NVarChar, id.trim()).query(`
              INSERT INTO Events_access (group_id, target_type, target_id)
              VALUES (@groupId, @targetType, @targetId)
            `);
        }
      } else {
        // 如果是 OWN 或 NULL，直接插入
        await pool
          .request()
          .input("groupId", sql.NVarChar, group_id)
          .input("targetType", sql.NVarChar, target_type)
          .input("targetId", sql.NVarChar, target_id).query(`
            INSERT INTO Events_access (group_id, target_type, target_id)
            VALUES (@groupId, @targetType, @targetId)
          `);
      }
    }

    return res.json({
      success: true,
      message: "群組權限設定更新成功",
    });
  } catch (error) {
    console.error("更新群組權限設定時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "更新群組權限設定時發生錯誤",
      error: error.message,
    });
  }
};

// 獲取使用者的行事曆授權設定
exports.getUserAccess = async (req, res) => {
  const { userId, userGroups } = req.query;

  if (!userId) {
    return res.status(400).json({
      success: false,
      message: "缺少使用者ID",
    });
  }

  try {
    const pool = await sql.connect(dbConfig);

    let groupIds;

    // 如果有傳入使用者群組，則直接使用
    if (
      userGroups &&
      typeof userGroups === "string" &&
      userGroups.trim() !== ""
    ) {
      const userGroupCodes = userGroups
        .split(",")
        .map((g) => g.trim())
        .filter((g) => g);

      if (userGroupCodes.length > 0) {
        groupIds = userGroupCodes.map((code) => `'${code}'`).join(",");
      }
    }

    // 如果沒有傳入群組或解析失敗，則從資料庫獲取
    if (!groupIds) {
      // 查詢使用者所屬的群組
      const userGroupsResult = await pool
        .request()
        .input("userId", sql.NVarChar, userId).query(`
          SELECT access as GroupCode
          FROM Users_Access
          WHERE id = @userId
        `);

      if (userGroupsResult.recordset.length === 0) {
        return res.json({
          success: true,
          data: [], // 使用者沒有所屬群組，返回空數組
        });
      }

      // 獲取使用者所屬群組的行事曆授權設定
      groupIds = userGroupsResult.recordset
        .map((group) => `'${group.GroupCode}'`)
        .join(",");
    }

    // 獲取使用者所屬群組的行事曆授權設定
    const accessResult = await pool.request().query(`
      SELECT 
        group_id, 
        target_type, 
        target_id
      FROM Events_access
      WHERE group_id IN (${groupIds})
    `);

    // 引入清理字串的函數
    const cleanStringsDeep = (obj) => {
      if (Array.isArray(obj)) {
        return obj.map(cleanStringsDeep);
      } else if (obj && typeof obj === "object") {
        const cleaned = {};
        for (const key in obj) {
          const val = obj[key];
          cleaned[key] =
            typeof val === "string" ? val.trim() : cleanStringsDeep(val);
        }
        return cleaned;
      } else {
        return obj;
      }
    };

    // 清理記錄集中的字串
    const cleanedRecordset = cleanStringsDeep(accessResult.recordset);

    // 按目標類型分組授權設定
    const accessByType = {};
    const accessTypeList = [];

    cleanedRecordset.forEach((access) => {
      if (!accessByType[access.target_type]) {
        accessByType[access.target_type] = [];
        // 將每個目標類型添加到列表中
        accessTypeList.push({
          target_type: access.target_type,
          target_id: access.target_id,
          group_id: access.group_id,
        });
      }
      accessByType[access.target_type].push(access);
    });

    // 檢查每種目標類型的授權情況
    const authorizedTypes = {};
    const targetTypes = ["user", "group", "department", "branch"];

    targetTypes.forEach((type) => {
      const typeAccess = accessByType[type] || [];

      // 檢查是否有全部訪問權限
      const hasAllAccess = typeAccess.some(
        (access) => access.target_id === "ALL"
      );

      // 檢查是否有 OWN 授權
      const hasOwnAccess = typeAccess.some(
        (access) =>
          access.target_id && access.target_id.trim().toUpperCase() === "OWN"
      );

      // 檢查是否有特定對象的授權
      const hasSpecificAccess = typeAccess.some(
        (access) =>
          access.target_id !== "ALL" &&
          access.target_id &&
          access.target_id.trim().toUpperCase() !== "OWN"
      );

      // 如果有任何一種授權，則該類型是授權的
      authorizedTypes[type] = hasAllAccess || hasOwnAccess || hasSpecificAccess;
    });

    return res.json({
      success: true,
      data: accessResult.recordset,
      authorizedTypes: authorizedTypes,
      accessTypeList: accessTypeList,
    });
  } catch (error) {
    console.error("獲取使用者行事曆授權設定時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "獲取使用者行事曆授權設定時發生錯誤",
      error: error.message,
    });
  }
};

// 獲取經過授權篩選的目標對象
exports.getAuthorizedTargets = async (req, res) => {
  const { userId, targetType, userGroups, userDep, userBranch } = req.query;

  if (!userId || !targetType) {
    return res.status(400).json({
      success: false,
      message: "缺少必要參數",
    });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 檢查使用者是否為管理員，使用從前端傳入的使用者群組資訊
    const isAdmin =
      typeof userGroups === "string" && userGroups.trim() !== ""
        ? userGroups
            .split(",")
            .map((g) =>
              g && typeof g === "string" ? g.trim().toLowerCase() : ""
            )
            .includes("admin")
        : false;

    // 如果是管理員，直接返回所有目標對象，不需要篩選
    if (isAdmin) {
      let targetItems = [];

      // 根據目標類型獲取對應的數據
      switch (targetType) {
        case "user":
          // 獲取所有啟用的使用者
          const usersResult = await pool.request().query(`
            SELECT ID, Name 
            FROM Users 
            WHERE Sts = '1' and id is not null
            ORDER BY Name
          `);
          targetItems = usersResult.recordset.map((user) => ({
            id: user.ID.trim(),
            name: user.Name.trim(),
            label: `${user.Name.trim()} (${user.ID.trim()})`,
            value: user.ID.trim(),
          }));
          break;

        case "group":
          // 獲取所有群組
          const groupsResult = await pool.request().query(`
            SELECT Code, Name 
            FROM Users_group
            ORDER BY Name
          `);
          targetItems = groupsResult.recordset.map((group) => ({
            id: group.Code.trim(),
            name: group.Name.trim(),
            label: group.Name.trim(),
            value: group.Code.trim(),
          }));
          break;

        case "department":
          // 獲取所有部門
          const depsResult = await pool.request().query(`
            SELECT Id, Name, Type
            FROM Dep
            ORDER BY Type, Name
          `);
          targetItems = depsResult.recordset.map((dep) => ({
            id: dep.Id.trim(),
            name: dep.Name.trim(),
            label: dep.Name.trim(),
            value: dep.Id.trim(),
          }));
          break;

        case "branch":
          // 獲取所有啟用的門市
          const branchesResult = await pool.request().query(`
            SELECT Cod_cust, Cod_name
            FROM Branch
            WHERE Sts = '1'
            ORDER BY Cod_name
          `);
          targetItems = branchesResult.recordset.map((branch) => ({
            id: branch.Cod_cust.trim(),
            name: branch.Cod_name.trim(),
            label: branch.Cod_name.trim(),
            value: branch.Cod_cust.trim(),
          }));
          break;

        default:
          return res.status(400).json({
            success: false,
            message: "不支援的目標類型",
          });
      }

      return res.json({
        success: true,
        data: targetItems,
      });
    }

    // 非管理員，需要根據授權設定進行篩選
    // 使用前端傳入的使用者資訊
    if (!userDep) {
      return res.status(400).json({
        success: false,
        message: "缺少使用者部門資訊",
      });
    }

    // 2. 使用從前端傳入的使用者群組
    let userGroupCodes = [];

    // 解析傳入的使用者群組
    if (typeof userGroups === "string" && userGroups.trim() !== "") {
      try {
        userGroupCodes = userGroups
          .split(",")
          .map((g) => (g && typeof g === "string" ? g.trim() : ""))
          .filter((g) => g);
      } catch (error) {
        console.error("解析使用者群組參數時發生錯誤:", error);
      }
    }

    if (userGroupCodes.length === 0) {
      // 使用者沒有所屬群組，只能看到自己
      let targetItems = [];

      if (targetType === "user") {
        // 只能看到自己
        const selfResult = await pool
          .request()
          .input("userId", sql.NVarChar, userId).query(`
            SELECT ID, Name FROM Users WHERE UPPER(LTRIM(RTRIM(ID))) = UPPER(LTRIM(RTRIM(@userId)))
          `);

        if (selfResult.recordset.length > 0) {
          const user = selfResult.recordset[0];
          targetItems = [
            {
              id: user.ID.trim(),
              name: user.Name.trim(),
              label: `${user.Name.trim()} (${user.ID.trim()})`,
              value: user.ID.trim(),
            },
          ];
        }
      } else if (targetType === "department" && userDep) {
        // 只能看到自己的部門
        const depResult = await pool
          .request()
          .input("depId", sql.NVarChar, userDep).query(`
            SELECT Id, Name FROM Dep WHERE UPPER(LTRIM(RTRIM(Id))) = UPPER(LTRIM(RTRIM(@depId)))
          `);

        if (depResult.recordset.length > 0) {
          const dep = depResult.recordset[0];
          targetItems = [
            {
              id: dep.Id.trim(),
              name: dep.Name.trim(),
              label: dep.Name.trim(),
              value: dep.Id.trim(),
            },
          ];
        }
      } else if (targetType === "branch" && userBranch) {
        // 只能看到自己的門市
        const branchResult = await pool
          .request()
          .input("branchId", sql.NVarChar, userBranch).query(`
            SELECT Cod_cust, Cod_name FROM Branch WHERE UPPER(LTRIM(RTRIM(Cod_cust))) = UPPER(LTRIM(RTRIM(@branchId))) AND Sts = '1'
          `);

        if (branchResult.recordset.length > 0) {
          const branch = branchResult.recordset[0];
          targetItems = [
            {
              id: branch.Cod_cust.trim(),
              name: branch.Cod_name.trim(),
              label: branch.Cod_name.trim(),
              value: branch.Cod_cust.trim(),
            },
          ];
        }
      }

      return res.json({
        success: true,
        data: targetItems,
      });
    }

    // 3. 獲取使用者所屬群組的行事曆授權設定
    const groupIds = userGroupCodes
      .map((groupCode) => `'${groupCode}'`)
      .join(",");

    // 如果沒有群組，則返回空結果
    if (!groupIds) {
      return res.json({
        success: true,
        data: [],
      });
    }

    const accessResult = await pool.request().query(`
      SELECT
        group_id,
        target_type,
        target_id
      FROM Events_access
      WHERE group_id IN (${groupIds}) AND target_type = '${targetType}'
    `);

    // 如果沒有相關的授權設定，或者有授權設定但只包含 OWN，只返回自己相關的項目
    const onlyHasOwnAccess =
      accessResult.recordset.length > 0 &&
      accessResult.recordset.every(
        (access) =>
          access.target_id && access.target_id.trim().toUpperCase() === "OWN"
      );

    // 檢查是否有明確為當前目標類型設定 OWN 授權
    const hasSpecificTypeOwnAccess = accessResult.recordset.some(
      (access) =>
        access.target_type &&
        access.target_type.trim() === targetType &&
        access.target_id &&
        access.target_id.trim().toUpperCase() === "OWN"
    );

    if (
      accessResult.recordset.length === 0 ||
      (onlyHasOwnAccess && hasSpecificTypeOwnAccess)
    ) {
      let targetItems = [];

      if (targetType === "user") {
        // 只能看到自己
        const selfResult = await pool
          .request()
          .input("userId", sql.NVarChar, userId).query(`
            SELECT ID, Name FROM Users WHERE UPPER(LTRIM(RTRIM(ID))) = UPPER(LTRIM(RTRIM(@userId)))
          `);

        if (selfResult.recordset.length > 0) {
          const user = selfResult.recordset[0];
          targetItems = [
            {
              id: user.ID.trim(),
              name: user.Name.trim(),
              label: `${user.Name.trim()} (${user.ID.trim()})`,
              value: user.ID.trim(),
            },
          ];
        }
      } else if (targetType === "department" && userDep) {
        // 只能看到自己的部門
        const depResult = await pool
          .request()
          .input("depId", sql.NVarChar, userDep).query(`
            SELECT Id, Name FROM Dep WHERE UPPER(LTRIM(RTRIM(Id))) = UPPER(LTRIM(RTRIM(@depId)))
          `);

        if (depResult.recordset.length > 0) {
          const dep = depResult.recordset[0];
          targetItems = [
            {
              id: dep.Id.trim(),
              name: dep.Name.trim(),
              label: dep.Name.trim(),
              value: dep.Id.trim(),
            },
          ];
        }
      } else if (targetType === "branch" && userBranch) {
        // 只能看到自己的門市
        const branchResult = await pool
          .request()
          .input("branchId", sql.NVarChar, userBranch).query(`
            SELECT Cod_cust, Cod_name FROM Branch WHERE UPPER(LTRIM(RTRIM(Cod_cust))) = UPPER(LTRIM(RTRIM(@branchId))) AND Sts = '1'
          `);

        if (branchResult.recordset.length > 0) {
          const branch = branchResult.recordset[0];
          targetItems = [
            {
              id: branch.Cod_cust.trim(),
              name: branch.Cod_name.trim(),
              label: branch.Cod_name.trim(),
              value: branch.Cod_cust.trim(),
            },
          ];
        }
      }

      return res.json({
        success: true,
        data: targetItems,
      });
    }

    // 4. 處理授權設定
    const accessSettings = accessResult.recordset;

    // 檢查是否有 OWN 授權，忽略大小寫和空格
    const hasOwnAccess = accessSettings.some(
      (access) =>
        access.target_id && access.target_id.trim().toUpperCase() === "OWN"
    );
    if (hasOwnAccess && targetType === "user") {
      // 如果是 user 類型且有 OWN 授權，只能看到自己
      const selfResult = await pool
        .request()
        .input("userId", sql.NVarChar, userId).query(`
          SELECT ID, Name FROM Users WHERE UPPER(LTRIM(RTRIM(ID))) = UPPER(LTRIM(RTRIM(@userId)))
        `);

      if (selfResult.recordset.length > 0) {
        const user = selfResult.recordset[0];
        const targetItems = [
          {
            id: user.ID.trim(),
            name: user.Name.trim(),
            label: `${user.Name.trim()} (${user.ID.trim()})`,
            value: user.ID.trim(),
          },
        ];

        return res.json({
          success: true,
          data: targetItems,
        });
      } else {
        return res.json({
          success: true,
          data: [],
        });
      }
    }

    // 檢查是否有全部訪問權限
    const hasAllAccess = accessSettings.some(
      (access) => access.target_id && access.target_id.trim() === "ALL"
    );
    if (hasAllAccess) {
      // 有全部訪問權限，獲取所有目標對象
      let targetItems = [];

      switch (targetType) {
        case "user":
          const usersResult = await pool.request().query(`
            SELECT ID, Name 
            FROM Users 
            WHERE Sts = '1'
            ORDER BY Name
          `);
          targetItems = usersResult.recordset.map((user) => ({
            id: user.ID.trim(),
            name: user.Name.trim(),
            label: `${user.Name.trim()} (${user.ID.trim()})`,
            value: user.ID.trim(),
          }));
          break;

        case "group":
          const groupsResult = await pool.request().query(`
            SELECT Code, Name 
            FROM Users_group
            ORDER BY Name
          `);
          targetItems = groupsResult.recordset.map((group) => ({
            id: group.Code.trim(),
            name: group.Name.trim(),
            label: group.Name.trim(),
            value: group.Code.trim(),
          }));
          break;

        case "department":
          const depsResult = await pool.request().query(`
            SELECT Id, Name, Type
            FROM Dep
            ORDER BY Type, Name
          `);
          targetItems = depsResult.recordset.map((dep) => ({
            id: dep.Id.trim(),
            name: dep.Name.trim(),
            label: dep.Name.trim(),
            value: dep.Id.trim(),
          }));
          break;

        case "branch":
          const branchesResult = await pool.request().query(`
            SELECT Cod_cust, Cod_name
            FROM Branch
            WHERE Sts = '1'
            ORDER BY Cod_name
          `);
          targetItems = branchesResult.recordset.map((branch) => ({
            id: branch.Cod_cust.trim(),
            name: branch.Cod_name.trim(),
            label: branch.Cod_name.trim(),
            value: branch.Cod_cust.trim(),
          }));
          break;
      }

      return res.json({
        success: true,
        data: targetItems,
      });
    }

    // 檢查其他類型的 OWN 授權
    if (hasOwnAccess && targetType !== "user") {
      // 檢查是否有明確為當前目標類型設定 OWN 授權
      const hasSpecificTypeOwnAccess = accessSettings.some(
        (access) =>
          access.target_type === targetType &&
          access.target_id &&
          access.target_id.trim().toUpperCase() === "OWN"
      );

      // 如果沒有明確為當前目標類型設定 OWN 授權，則返回空數組
      if (!hasSpecificTypeOwnAccess) {
        return res.json({
          success: true,
          data: [],
        });
      }

      let targetItems = [];

      switch (targetType) {
        case "department":
          // 只能看到自己的部門，使用前端傳入的部門資訊
          if (userDep) {
            const depResult = await pool
              .request()
              .input("depId", sql.NVarChar, userDep).query(`
                SELECT Id, Name FROM Dep WHERE UPPER(LTRIM(RTRIM(Id))) = UPPER(LTRIM(RTRIM(@depId)))
              `);

            if (depResult.recordset.length > 0) {
              const dep = depResult.recordset[0];
              targetItems = [
                {
                  id: dep.Id.trim(),
                  name: dep.Name.trim(),
                  label: dep.Name.trim(),
                  value: dep.Id.trim(),
                },
              ];
            }
          }
          break;

        case "branch":
          // 只能看到自己的門市，使用前端傳入的門市資訊
          if (userBranch) {
            const branchResult = await pool
              .request()
              .input("branchId", sql.NVarChar, userBranch).query(`
                SELECT Cod_cust, Cod_name FROM Branch WHERE UPPER(LTRIM(RTRIM(Cod_cust))) = UPPER(LTRIM(RTRIM(@branchId))) AND Sts = '1'
              `);

            if (branchResult.recordset.length > 0) {
              const branch = branchResult.recordset[0];
              targetItems = [
                {
                  id: branch.Cod_cust.trim(),
                  name: branch.Cod_name.trim(),
                  label: branch.Cod_name.trim(),
                  value: branch.Cod_cust.trim(),
                },
              ];
            }
          }
          break;

        case "group":
          // 使用前端傳入的使用者群組，但只有在明確設定 OWN 授權時才顯示
          if (userGroupCodes.length > 0) {
            // 查詢群組名稱
            const groupIdsStr = userGroupCodes
              .map((code) => `'${code}'`)
              .join(",");
            const groupsResult = await pool.request().query(`
              SELECT Code, Name 
              FROM Users_group
              WHERE Code IN (${groupIdsStr})
              ORDER BY Name
            `);

            targetItems = groupsResult.recordset.map((group) => ({
              id: group.Code.trim(),
              name: group.Name.trim(),
              label: group.Name.trim(),
              value: group.Code.trim(),
            }));
          }
          break;
      }

      return res.json({
        success: true,
        data: targetItems,
      });
    }

    // 處理特定對象的授權
    const specificTargetIds = accessSettings
      .filter(
        (access) =>
          access.target_id !== "ALL" &&
          access.target_id &&
          access.target_id.trim().toUpperCase() !== "OWN"
      )
      .map((access) => access.target_id);

    if (specificTargetIds.length > 0) {
      // 合併所有特定目標ID
      const allTargetIds = specificTargetIds
        .join(",")
        .split(",")
        .map((id) => id.trim());
      const uniqueTargetIds = [...new Set(allTargetIds)];

      if (uniqueTargetIds.length === 0) {
        return res.json({
          success: true,
          data: [],
        });
      }

      // 構建 IN 查詢條件
      const targetIdsCondition = uniqueTargetIds
        .map((id) => `'${id}'`)
        .join(",");

      let targetItems = [];

      switch (targetType) {
        case "user":
          const usersResult = await pool.request().query(`
            SELECT ID, Name 
            FROM Users 
            WHERE ID IN (${targetIdsCondition}) AND Sts = '1'
            ORDER BY Name
          `);
          targetItems = usersResult.recordset.map((user) => ({
            id: user.ID.trim(),
            name: user.Name.trim(),
            label: `${user.Name.trim()} (${user.ID.trim()})`,
            value: user.ID.trim(),
          }));
          break;

        case "group":
          const groupsResult = await pool.request().query(`
            SELECT Code, Name 
            FROM Users_group
            WHERE Code IN (${targetIdsCondition})
            ORDER BY Name
          `);
          targetItems = groupsResult.recordset.map((group) => ({
            id: group.Code.trim(),
            name: group.Name.trim(),
            label: group.Name.trim(),
            value: group.Code.trim(),
          }));
          break;

        case "department":
          const depsResult = await pool.request().query(`
            SELECT Id, Name
            FROM Dep
            WHERE Id IN (${targetIdsCondition})
            ORDER BY Name
          `);
          targetItems = depsResult.recordset.map((dep) => ({
            id: dep.Id.trim(),
            name: dep.Name.trim(),
            label: dep.Name.trim(),
            value: dep.Id.trim(),
          }));
          break;

        case "branch":
          const branchesResult = await pool.request().query(`
            SELECT Cod_cust, Cod_name
            FROM Branch
            WHERE Cod_cust IN (${targetIdsCondition}) AND Sts = '1'
            ORDER BY Cod_name
          `);
          targetItems = branchesResult.recordset.map((branch) => ({
            id: branch.Cod_cust.trim(),
            name: branch.Cod_name.trim(),
            label: branch.Cod_name.trim(),
            value: branch.Cod_cust.trim(),
          }));
          break;
      }

      return res.json({
        success: true,
        data: targetItems,
      });
    }

    // 如果沒有匹配的授權設定，返回空數組
    return res.json({
      success: true,
      data: [],
    });
  } catch (error) {
    console.error("獲取授權目標對象時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "獲取授權目標對象時發生錯誤",
      error: error.message,
    });
  }
};
