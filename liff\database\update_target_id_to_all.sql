-- 更新 Events_access 表中的 target_id 從 NULL 改為 'ALL'
-- 這個腳本用於將「所有對象」的表示從 NULL 改為 'ALL'

-- 更新 Events_access 表
UPDATE Events_access 
SET target_id = 'ALL' 
WHERE target_id IS NULL;

-- 更新 Events_targets 表（如果存在）
UPDATE Events_targets 
SET target_id = 'ALL' 
WHERE target_id IS NULL;

-- 檢查更新結果
SELECT 
    'Events_access' as table_name,
    COUNT(*) as all_count,
    SUM(CASE WHEN target_id = 'ALL' THEN 1 ELSE 0 END) as all_targets,
    SUM(CASE WHEN target_id = 'OWN' THEN 1 ELSE 0 END) as own_targets,
    SUM(CASE WHEN target_id NOT IN ('ALL', 'OWN') THEN 1 ELSE 0 END) as specific_targets
FROM Events_access

UNION ALL

SELECT 
    'Events_targets' as table_name,
    COUNT(*) as all_count,
    SUM(CASE WHEN target_id = 'ALL' THEN 1 ELSE 0 END) as all_targets,
    SUM(CASE WHEN target_id = 'OWN' THEN 1 ELSE 0 END) as own_targets,
    SUM(CASE WHEN target_id NOT IN ('ALL', 'OWN') THEN 1 ELSE 0 END) as specific_targets
FROM Events_targets;
