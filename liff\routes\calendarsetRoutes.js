const express = require("express");
const router = express.Router();
const calendarsetController = require("../controllers/calendarsetController");
const authMiddleware = require("../middleware/authMiddleware");

// 所有路由都需要驗證
router.use(authMiddleware);

// 獲取部門顏色設定
router.get("/department_colors", calendarsetController.getDepartmentColors);

// 更新部門顏色
router.post(
  "/update_department_color",
  calendarsetController.updateDepartmentColor
);

// 獲取群組的權限設定
router.get("/group_access/:groupId", calendarsetController.getGroupAccess);

// 更新群組的權限設定
router.post("/update_group_access", calendarsetController.updateGroupAccess);

// 獲取使用者的行事曆授權設定
router.get("/user_access", calendarsetController.getUserAccess);

// 獲取經過授權篩選的目標對象
router.get("/authorized_targets", calendarsetController.getAuthorizedTargets);

module.exports = router;
