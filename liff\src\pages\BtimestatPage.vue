<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 標題和搜尋區域 -->
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題與搜尋 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">時段統計</div>
        </div>
      </q-card-section>

      <!-- 日期選擇區塊 -->
      <q-card-section class="q-pt-none">
        <div class="row items-center no-wrap">
          <!-- 日期區間輸入框 -->
          <q-input
            v-model="formattedDateText"
            label="選擇日期區間（可多段）"
            dense
            outlined
            square
            class="col"
            readonly
          >
            <!-- 左側 icon -->
            <template v-slot:prepend>
              <q-icon name="event" size="sm" />
            </template>

            <!-- 日期選擇 popup -->
            <template v-slot:append>
              <q-icon
                name="calendar_month"
                color="primary"
                class="cursor-pointer"
                size="18px"
              >
                <q-popup-proxy
                  transition-show="scale"
                  transition-hide="scale"
                  cover
                >
                  <q-date
                    v-model="dateRanges"
                    range
                    multiple
                    mask="YYYY-MM-DD"
                    emit-immediately
                  >
                    <div
                      class="row items-center justify-end q-gutter-sm q-pa-sm"
                    >
                      <q-btn label="確定" color="primary" flat v-close-popup />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>

          <!-- 篩選按鈕 -->
          <q-btn
            flat
            round
            size="sm"
            color="red"
            class="search-button q-ml-sm"
            @click="handleSearch"
          >
            <Search size="20" />
            <q-tooltip>篩選統計</q-tooltip>
          </q-btn>
        </div>
      </q-card-section>

      <!-- 查詢區間顯示 -->
      <q-card-section v-if="pendingPayload" class="q-pt-none q-pb-xs">
        <q-chip outline color="teal" class="full-width justify-center">
          <q-icon name="date_range" class="q-mr-xs" />
          已選擇 {{ expandDateRanges(dateRanges).length }} 天（{{
            dateRanges.length
          }}
          段）
        </q-chip>
      </q-card-section>
    </q-card>

    <!-- 主內容區域 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <!-- 載入中狀態 -->
      <div v-if="isLoading" class="q-pa-xl flex flex-center">
        <div class="fancy-loader">
          <svg
            class="loader-svg"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle class="loader-circle" cx="50" cy="50" r="40" />
          </svg>
          <div class="loader-message">資料整理中...</div>
        </div>
      </div>

      <!-- 無資料顯示 -->
      <q-card-section
        v-else-if="tableData.length === 0 && pendingPayload && !isLoading"
        class="empty-state-container"
      >
        <div class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24">
            <path
              d="M13,9h-2v2H9v2h2v2h2v-2h2v-2h-2V9z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20 M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z"
            />
          </svg>
          <div class="empty-text">查無時段統計資料</div>
          <div class="empty-subtext">
            請嘗試更改查詢條件或選擇不同的日期區間
          </div>
        </div>
      </q-card-section>

      <!-- Tab 切換區 -->
      <q-tabs
        v-model="activeTab"
        dense
        class="text-grey q-pt-md q-pb-none q-mb-md"
        active-color="green"
        align="justify"
        v-if="tableData.length > 0"
      >
        <q-tab name="chart" icon="bar_chart" label="圖表" />
        <q-tab name="detail" icon="list_alt" label="明細" />
      </q-tabs>

      <q-tab-panels
        v-model="activeTab"
        animated
        keep-alive
        v-if="tableData.length > 0"
      >
        <!-- 圖表頁 -->
        <q-tab-panel name="chart" class="q-pa-none">
          <q-card-section>
            <div ref="chartRef" style="height: 400px"></div>
          </q-card-section>
        </q-tab-panel>

        <!-- 明細頁 -->
        <q-tab-panel name="detail" class="q-pa-none">
          <q-card-section class="q-pa-none">
            <q-list dense bordered separator>
              <q-item
                v-for="row in tableData"
                :key="row.hour"
                class="time-item"
              >
                <q-item-section avatar>
                  <q-icon name="schedule" color="deep-orange" />
                </q-item-section>
                <q-item-section>
                  <span class="text-subtitle2 text-weight-medium">{{
                    row.hour
                  }}</span>
                </q-item-section>
                <q-item-section side class="text-right">
                  <div>
                    <div class="text-subtitle2">
                      {{ row.persons }} 人
                      <q-badge outline color="primary" class="q-ml-xs">
                        {{ calculatePercentage(row.persons) }}%
                      </q-badge>
                    </div>
                    <div class="text-caption text-grey-7">
                      {{ row.amount.toLocaleString() }} 元
                    </div>
                  </div>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-tab-panel>
      </q-tab-panels>
    </q-card>

    <!-- 統計方式對話框 -->
    <q-dialog v-model="showModeDialog" class="filter-dialog">
      <q-card style="min-width: 350px; max-width: 90vw; border-radius: 10px">
        <!-- 標題區 -->
        <q-card-section class="row items-center q-gutter-sm">
          <q-avatar icon="leaderboard" color="primary" text-color="white" />
          <div class="text-h6 text-primary">統計方式</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <!-- 統計選項 -->
        <q-card-section class="q-pt-md q-gutter-md">
          <div class="text-subtitle2 text-grey-8 q-mb-xs">請選擇統計方式</div>
          <q-option-group
            v-model="statMode"
            :options="[
              { label: '合計總數', value: 'sum' },
              { label: '平均每日期', value: 'avg' },
            ]"
            type="radio"
            color="primary"
          />
        </q-card-section>

        <q-separator />

        <!-- 動作按鈕 -->
        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="取消" v-close-popup class="text-grey-7" />
          <q-btn
            unelevated
            color="primary"
            label="查詢"
            @click="
              showModeDialog = false;
              loadData();
            "
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { DateTime } from "luxon";
import * as echarts from "echarts";
import { useQuasar } from "quasar";

const $q = useQuasar();
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const props = defineProps({
  Branch: String,
});

// 初始化變數
const statMode = ref("sum"); // sum 或 avg
const showModeDialog = ref(false);
const pendingPayload = ref(null); // 暫存查詢參數
const isLoading = ref(false);
const tableData = ref([]);
const activeTab = ref("chart");
const chartRef = ref(null);
let myChart;
const today = DateTime.now().toFormat("yyyy-MM-dd");
const dateRanges = ref([]);

// 處理查詢
const handleSearch = () => {
  const expandedDates = expandDateRanges(dateRanges.value);
  if (expandedDates.length === 0) {
    $q.notify({ type: "warning", message: "請選擇有效的日期區間" });
    return;
  }

  pendingPayload.value = {
    cod_cust: props.Branch,
    dates: expandedDates,
  };
  showModeDialog.value = true; // 開啟統計方式選擇對話框
};

// 計算人數百分比
const calculatePercentage = (persons) => {
  const total = tableData.value.reduce((sum, item) => sum + item.persons, 0);
  return total ? Math.round((persons / total) * 100) : 0;
};

// 載入資料
const loadData = async () => {
  if (!pendingPayload.value) return;

  isLoading.value = true;
  const payload = pendingPayload.value;
  const expandedDates = payload.dates;
  const dayCount = expandedDates.length;

  try {
    const res = await apiClient.post(`${apiBaseUrl}/btimestat/get_TimeStat`, {
      payload,
    });

    const result = res.data.data || [];
    const useAvg = statMode.value === "avg";

    const averageData = result.map((item) => ({
      hour: `${item.Hour}:00`,
      persons: useAvg ? Math.round(item.Persons / dayCount) : item.Persons,
      amount: useAvg ? Math.round(item.Amount / dayCount) : item.Amount,
    }));

    // 設置圖表
    const option = {
      tooltip: {
        trigger: "axis",
        formatter: (params) => {
          let content = `${params[0].axisValue}<br/>`;
          params.forEach((p) => {
            const val =
              p.seriesName === "金額"
                ? (p.data / 1000).toFixed(1) + "k"
                : p.data;
            content += `${p.marker} ${p.seriesName}：${val}<br/>`;
          });
          return content;
        },
      },
      legend: { data: ["來客數", "金額"] },
      xAxis: {
        type: "category",
        data: averageData.map((item) => item.hour),
      },
      yAxis: [
        { type: "value", name: "來客數" },
        {
          type: "value",
          name: "金額",
          position: "right",
          axisLabel: {
            formatter: (val) => `${(val / 1000).toFixed(1)}k`,
          },
        },
      ],
      series: [
        {
          name: "來客數",
          type: "bar",
          data: averageData.map((item) => item.persons),
          itemStyle: {
            color: "#ff7043", // 深橙色
          },
        },
        {
          name: "金額",
          type: "bar",
          yAxisIndex: 1,
          data: averageData.map((item) => item.amount),
          itemStyle: {
            color: "#26a69a", // 水鴨色
          },
        },
      ],
    };

    tableData.value = averageData;
    isLoading.value = false;

    // 延遲初始化圖表，確保 DOM 已渲染
    setTimeout(() => {
      if (chartRef.value) {
        if (myChart) {
          myChart.dispose();
        }
        myChart = echarts.init(chartRef.value);
        myChart.setOption(option);
      }
    }, 50);
  } catch (err) {
    console.error("❌ API 錯誤", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
    isLoading.value = false;
  }
};

const formattedDateText = computed(() => {
  if (!Array.isArray(dateRanges.value) || dateRanges.value.length === 0) {
    return "請選擇日期";
  }

  let totalDays = 0;

  for (const item of dateRanges.value) {
    if (typeof item === "string") {
      // ✅ 單日：直接加 1 天
      totalDays += 1;
    } else if (item.from && item.to) {
      const d1 = new Date(item.from);
      const d2 = new Date(item.to);
      const diff = Math.floor((d2 - d1) / (1000 * 60 * 60 * 24)) + 1; // 含當天
      if (diff > 0) totalDays += diff;
    }
  }

  return `已選 ${totalDays} 天（${dateRanges.value.length} 段）`;
});

function expandDateRanges(dateRanges) {
  const dates = new Set();

  for (const item of dateRanges) {
    if (typeof item === "string") {
      // ✅ 單日：也轉成 yyyy/MM/dd
      const formatted = DateTime.fromISO(item).toFormat("yyyy/MM/dd");
      dates.add(formatted);
    } else if (item.from && item.to) {
      let current = DateTime.fromISO(item.from);
      const end = DateTime.fromISO(item.to);

      while (current <= end) {
        dates.add(current.toFormat("yyyy/MM/dd")); // ✅ ← 轉斜線格式
        current = current.plus({ days: 1 });
      }
    }
  }

  return Array.from(dates).sort();
}

// 監聽 tab 切換，重新調整圖表大小
watch(activeTab, (newVal) => {
  if (newVal === "chart" && myChart) {
    setTimeout(() => {
      myChart.resize();
    }, 100); // 延遲執行，確保 DOM 已顯示
  }
});

// 監聽窗口大小變化，重新調整圖表大小
const handleResize = () => {
  if (myChart) {
    myChart.resize();
  }
};

// 初始化：設置今天的日期與監聽窗口大小變化
onMounted(() => {
  dateRanges.value = [
    {
      from: today,
      to: today,
    },
  ];

  window.addEventListener("resize", handleResize);
});

// 組件卸載時移除事件監聽
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});
</script>

<style>
.fixed-top-container {
  position: fixed;
  top: 60px; /* 避免與工具列重疊 */
  left: 0;
  right: 0;
  z-index: 1;
  padding: 0.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.title-section {
  padding-bottom: 8px;
}

.search-button {
  min-height: 32px;
  min-width: 32px;
}

.time-item:hover {
  background-color: rgba(255, 236, 225, 0.5);
}

.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #ff9800;
  stroke-width: 5;
  stroke-dasharray: 283;
  stroke-linecap: round;
  transform-origin: 50% 50%;
  animation: dash 1.5s ease-in-out infinite;
}

.loader-message {
  margin-top: 1rem;
  color: #ff9800;
  font-size: 0.9rem;
}

.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  fill: #bdbdbd;
  margin-bottom: 0.5rem;
}

.empty-text {
  font-size: 1rem;
  color: #616161;
  margin-bottom: 0.25rem;
}

.empty-subtext {
  font-size: 0.8rem;
  color: #9e9e9e;
}

.filter-dialog {
  border-radius: 10px;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 283;
  }
  50% {
    stroke-dashoffset: 70;
  }
  100% {
    stroke-dashoffset: 283;
  }
}
</style>
